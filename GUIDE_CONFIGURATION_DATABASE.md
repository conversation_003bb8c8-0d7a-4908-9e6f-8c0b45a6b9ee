# Guide de Configuration de la Base de Données ClockIn

## 📋 Vue d'ensemble

Ce guide vous explique comment configurer et connecter votre projet Laravel 12.x à une base de données MySQL nommée `clockin` hébergée sur WampServer.

## 🔧 Prérequis

- **WampServer** installé et fonctionnel
- **PHP 8.3+** (inclus avec WampServer)
- **MySQL 8.0+** (inclus avec WampServer)
- **Composer** installé

## ⚙️ Configuration

### 1. Vérification de WampServer

Avant de commencer, assurez-vous que WampServer fonctionne correctement :

1. **Démarrez WampServer** - L'icône doit être **verte**
2. **Vérifiez les services** :
   - Apache : En cours d'exécution
   - MySQL : En cours d'exécution
3. **Testez l'accès** : Ouvrez http://localhost:8080/phpmyadmin

### 2. Configuration du fichier .env

Le fichier `.env` est déjà configuré avec les paramètres optimaux :

```env
# Configuration de la base de données
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=clockin
DB_USERNAME=root
DB_PASSWORD=
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci
DB_ENGINE=InnoDB
```

### 3. Configuration avancée (config/database.php)

La configuration MySQL a été optimisée pour WampServer avec :

- **Encodage** : UTF-8MB4 avec collation unicode
- **Engine** : InnoDB par défaut
- **Timeout** : 60 secondes
- **Mode SQL** : Compatible avec MySQL 8.0+
- **Gestion d'erreurs** : Exceptions PDO

## 🧪 Tests de Connexion

### Commande de test intégrée

Utilisez la commande personnalisée pour tester la connexion :

```bash
# Test simple de connexion
php artisan db:test-connection

# Test avec création automatique de la base
php artisan db:test-connection --create-db
```

### Script de configuration autonome

Vous pouvez également utiliser le script PHP autonome :

```bash
php database/scripts/setup_database.php
```

## 🗄️ Gestion des Migrations

### Vérifier l'état des migrations

```bash
php artisan migrate:status
```

### Exécuter les migrations

```bash
# Première installation
php artisan migrate

# Réinitialisation complète (supprime toutes les données)
php artisan migrate:fresh

# Rollback et re-migration
php artisan migrate:refresh
```

## 🚨 Résolution des Problèmes Courants

### Erreur : "Connection refused"

**Cause** : MySQL n'est pas démarré

**Solution** :
1. Vérifiez que WampServer est démarré (icône verte)
2. Redémarrez le service MySQL depuis WampServer
3. Vérifiez le port 3306 dans WampServer

### Erreur : "Access denied for user 'root'"

**Cause** : Problème d'authentification

**Solution** :
1. Vérifiez les identifiants dans `.env`
2. Testez la connexion via phpMyAdmin
3. Réinitialisez le mot de passe root si nécessaire

### Erreur : "Unknown database 'clockin'"

**Cause** : La base de données n'existe pas

**Solution** :
```bash
# Création automatique
php artisan db:test-connection --create-db

# Ou création manuelle via phpMyAdmin
```

### Erreur : "Table already exists"

**Cause** : Tables déjà présentes dans la base

**Solution** :
```bash
# Réinitialisation complète
php artisan migrate:fresh
```

### Erreur : "sql_mode" incompatible

**Cause** : Mode SQL incompatible avec MySQL 8.0+

**Solution** : Déjà corrigée dans la configuration. Le mode `NO_AUTO_CREATE_USER` a été retiré.

## 📊 Structure de la Base de Données

### Tables créées

1. **users** - Utilisateurs (employés et administrateurs)
2. **sites** - Chantiers/sites de travail
3. **assignments** - Assignations employé-site
4. **pointages** - Enregistrements de pointage
5. **verifications** - Demandes de vérification
6. **logs_pointages** - Logs des tentatives de pointage
7. **migrations** - Suivi des migrations Laravel
8. **cache** - Cache de l'application
9. **jobs** - Queue des tâches
10. **sessions** - Sessions utilisateur

### Caractéristiques techniques

- **Encodage** : UTF8MB4 (support emoji et caractères spéciaux)
- **Collation** : utf8mb4_unicode_ci (tri Unicode)
- **Engine** : InnoDB (transactions et clés étrangères)
- **Clés étrangères** : CASCADE pour maintenir l'intégrité
- **Index** : Optimisés pour les requêtes fréquentes

## 🔍 Vérification Post-Installation

### 1. Test de connexion

```bash
php artisan db:test-connection
```

### 2. Vérification des tables

```bash
php artisan migrate:status
```

### 3. Test d'insertion (optionnel)

```bash
php artisan tinker
```

Puis dans Tinker :
```php
// Test d'insertion d'un utilisateur
App\Models\User::create([
    'nom' => 'Test Admin',
    'email' => '<EMAIL>',
    'mot_de_passe' => bcrypt('password'),
    'role' => 'admin'
]);
```

## 📝 Commandes Utiles

```bash
# Test de connexion
php artisan db:test-connection

# État des migrations
php artisan migrate:status

# Exécution des migrations
php artisan migrate

# Réinitialisation complète
php artisan migrate:fresh

# Accès à la console Laravel
php artisan tinker

# Nettoyage du cache
php artisan config:clear
php artisan cache:clear
```

## 🎯 Prochaines Étapes

Une fois la base de données configurée avec succès :

1. **Configurez JWT** pour l'authentification
2. **Installez les packages** supplémentaires (Swagger, Firebase)
3. **Créez les seeders** pour les données de test
4. **Configurez les tests** unitaires
5. **Implémentez les contrôleurs** API

## 📞 Support

En cas de problème persistant :

1. Vérifiez les logs Laravel : `storage/logs/laravel.log`
2. Vérifiez les logs MySQL dans WampServer
3. Testez la connexion directe via phpMyAdmin
4. Consultez la documentation Laravel pour les problèmes spécifiques

---

✅ **Configuration terminée avec succès !** Votre projet Laravel est maintenant connecté à la base de données MySQL `clockin` sur WampServer.
