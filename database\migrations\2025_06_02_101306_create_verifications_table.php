<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('verifications', function (Blueprint $table) {
            $table->id('id_verification');
            $table->unsignedBigInteger('id_user');
            $table->unsignedBigInteger('id_site');
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->dateTime('date_verification')->nullable();
            $table->enum('status', ['pending', 'verified', 'failed'])->default('pending');
            $table->string('resultat')->nullable(); // Résultat de la vérification
            $table->decimal('distance_calculee', 8, 2)->nullable(); // Distance calculée en mètres
            $table->timestamps();

            // Clés étrangères avec CASCADE
            $table->foreign('id_user')->references('id_user')->on('users')->onDelete('cascade');
            $table->foreign('id_site')->references('id_site')->on('sites')->onDelete('cascade');

            // Index pour optimiser les performances
            $table->index('id_user');
            $table->index('id_site');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('verifications');
    }
};
