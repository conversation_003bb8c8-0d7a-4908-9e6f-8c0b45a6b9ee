name: 'Autres Endpoints'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/users
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of the resource.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/users/{id}'
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified resource.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the user.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/users/{id}'
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified resource in storage.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the user.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      nom:
        name: nom
        description: validation.between.
        required: false
        example: bng
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      email:
        name: email
        description: ''
        required: false
        example: null
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      date_embauche:
        name: date_embauche
        description: validation.date.
        required: false
        example: '2025-06-03T14:59:57'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      numero_cnss:
        name: numero_cnss
        description: validation.max.
        required: false
        example: z
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      nom: bng
      date_embauche: '2025-06-03T14:59:57'
      numero_cnss: z
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/users/{id}'
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified resource from storage.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the user.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/users/{id}/pointages'
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Obtenir les pointages d'un utilisateur"
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the user.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/sites
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of the resource.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/sites
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created resource in storage.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      nom_site:
        name: nom_site
        description: validation.max.
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      latitude:
        name: latitude
        description: validation.between.
        required: true
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      longitude:
        name: longitude
        description: validation.between.
        required: true
        example: -180
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      nom_site: b
      latitude: -89
      longitude: -180
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/sites/{id}'
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified resource.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the site.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/sites/{id}'
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified resource in storage.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the site.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      nom_site:
        name: nom_site
        description: ''
        required: false
        example: null
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      latitude:
        name: latitude
        description: validation.between.
        required: false
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      longitude:
        name: longitude
        description: validation.between.
        required: false
        example: -179
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      latitude: -89
      longitude: -179
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/sites/{id}'
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified resource from storage.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the site.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/sites/{id}/pointages'
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Obtenir les pointages d'un site"
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the site.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/sites/{id}/verify-location'
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Vérifier la localisation par rapport au site'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the site.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      latitude:
        name: latitude
        description: validation.between.
        required: true
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      longitude:
        name: longitude
        description: validation.between.
        required: true
        example: -179
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      latitude: -89
      longitude: -179
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/users
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created resource in storage.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      nom:
        name: nom
        description: validation.between.
        required: true
        example: bng
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      email:
        name: email
        description: 'validation.email validation.max.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      mot_de_passe:
        name: mot_de_passe
        description: validation.min.
        required: true
        example: dljnikhwaykcmyuwpwlvqwrsitcpscqldzsnrwtujwvlxj
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      role:
        name: role
        description: ''
        required: true
        example: admin
        type: string
        enumValues:
          - admin
          - employee
        exampleWasSpecified: false
        nullable: false
        custom: []
      date_embauche:
        name: date_embauche
        description: validation.date.
        required: false
        example: '2025-06-03T14:59:58'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      numero_cnss:
        name: numero_cnss
        description: validation.max.
        required: false
        example: k
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      nom: bng
      email: <EMAIL>
      mot_de_passe: dljnikhwaykcmyuwpwlvqwrsitcpscqldzsnrwtujwvlxj
      role: admin
      date_embauche: '2025-06-03T14:59:58'
      numero_cnss: k
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/employees
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Obtenir la liste des employés'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/employees
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created resource in storage.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      nom:
        name: nom
        description: validation.between.
        required: true
        example: bng
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      email:
        name: email
        description: 'validation.email validation.max.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      mot_de_passe:
        name: mot_de_passe
        description: validation.min.
        required: true
        example: dljnikhwaykcmyuwpwlvqwrsitcpscqldzsnrwtujwvlxj
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      role:
        name: role
        description: ''
        required: true
        example: admin
        type: string
        enumValues:
          - admin
          - employee
        exampleWasSpecified: false
        nullable: false
        custom: []
      date_embauche:
        name: date_embauche
        description: validation.date.
        required: false
        example: '2025-06-03T14:59:58'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      numero_cnss:
        name: numero_cnss
        description: validation.max.
        required: false
        example: k
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      nom: bng
      email: <EMAIL>
      mot_de_passe: dljnikhwaykcmyuwpwlvqwrsitcpscqldzsnrwtujwvlxj
      role: admin
      date_embauche: '2025-06-03T14:59:58'
      numero_cnss: k
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/employees/{id}'
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified resource.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the employee.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/employees/{id}'
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified resource in storage.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the employee.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      nom:
        name: nom
        description: validation.between.
        required: false
        example: bng
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      email:
        name: email
        description: ''
        required: false
        example: null
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      date_embauche:
        name: date_embauche
        description: validation.date.
        required: false
        example: '2025-06-03T14:59:58'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      numero_cnss:
        name: numero_cnss
        description: validation.max.
        required: false
        example: z
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      nom: bng
      date_embauche: '2025-06-03T14:59:58'
      numero_cnss: z
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/employees/{id}'
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified resource from storage.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the employee.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/users/admins
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Obtenir la liste des administrateurs'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/sites
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created resource in storage.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      nom_site:
        name: nom_site
        description: validation.max.
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      latitude:
        name: latitude
        description: validation.between.
        required: true
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      longitude:
        name: longitude
        description: validation.between.
        required: true
        example: -180
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      nom_site: b
      latitude: -89
      longitude: -180
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/assign-site
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Assigner un utilisateur à un site'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      id_user:
        name: id_user
        description: 'The <code>id_user</code> of an existing record in the users table.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      id_site:
        name: id_site
        description: 'The <code>id_site</code> of an existing record in the sites table.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      id_user: architecto
      id_site: architecto
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/assignments
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Assigner un utilisateur à un site'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      id_user:
        name: id_user
        description: 'The <code>id_user</code> of an existing record in the users table.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      id_site:
        name: id_site
        description: 'The <code>id_site</code> of an existing record in the sites table.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      id_user: architecto
      id_site: architecto
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/assignments/{userId}/{siteId}'
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Retirer un utilisateur d'un site"
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      userId:
        name: userId
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      siteId:
        name: siteId
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      userId: architecto
      siteId: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/test
    metadata:
      groupName: 'Autres Endpoints'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"message":"ClockIn API is working!","version":"1.0.0","timestamp":"2025-06-03T14:59:58.861544Z"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
