<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\PointageController;
use App\Http\Controllers\SiteController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\LocationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Routes publiques (sans authentification)
Route::prefix('auth')->group(function () {
    Route::post('login', [AuthController::class, 'login']);
    Route::post('register', [AuthController::class, 'register']);
});

// Routes protégées (avec authentification JWT)
Route::middleware('auth:api')->group(function () {
    
    // Authentification
    Route::prefix('auth')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::post('refresh', [AuthController::class, 'refresh']);
        Route::get('me', [AuthController::class, 'me']);
    });

    // Vérification de localisation (pour tous les utilisateurs authentifiés)
    Route::prefix('location')->group(function () {
        Route::post('check-location', [LocationController::class, 'checkLocation']);
        Route::post('request-verification', [LocationController::class, 'requestVerification']);
        Route::get('verifications', [LocationController::class, 'getVerifications']);
    });

    // Gestion des utilisateurs
    Route::prefix('users')->group(function () {
        Route::get('/', [UserController::class, 'index']);
        Route::get('/{id}', [UserController::class, 'show']);
        Route::put('/{id}', [UserController::class, 'update']);
        Route::delete('/{id}', [UserController::class, 'destroy']);
        Route::get('/{id}/pointages', [UserController::class, 'pointages']);
    });

    // Gestion des sites
    Route::prefix('sites')->group(function () {
        Route::get('/', [SiteController::class, 'index']);
        Route::post('/', [SiteController::class, 'store']);
        Route::get('/{id}', [SiteController::class, 'show']);
        Route::put('/{id}', [SiteController::class, 'update']);
        Route::delete('/{id}', [SiteController::class, 'destroy']);
        Route::get('/{id}/pointages', [SiteController::class, 'pointages']);
        Route::post('/{id}/verify-location', [SiteController::class, 'verifyLocation']);
    });

    // Gestion des pointages (employés et admins)
    Route::prefix('pointages')->middleware('role:employee,admin')->group(function () {
        Route::get('/', [PointageController::class, 'index']);
        Route::post('/', [PointageController::class, 'store']);
        Route::get('/{id}', [PointageController::class, 'show']);
        Route::put('/{id}', [PointageController::class, 'update']);
        Route::delete('/{id}', [PointageController::class, 'destroy'])->middleware('role:admin');

        // Actions spécifiques aux pointages (employés)
        Route::post('save-pointage', [PointageController::class, 'startPointage']); // Endpoint spécifié
        Route::post('start', [PointageController::class, 'startPointage']);
        Route::post('end', [PointageController::class, 'endPointage']);
        Route::get('current/status', [PointageController::class, 'getCurrentStatus']);
        Route::get('user/{userId}', [PointageController::class, 'getUserPointages']);
        Route::get('site/{siteId}', [PointageController::class, 'getSitePointages']);
        Route::get('reports/daily', [PointageController::class, 'dailyReport']);
        Route::get('reports/weekly', [PointageController::class, 'weeklyReport']);
        Route::get('reports/monthly', [PointageController::class, 'monthlyReport']);
    });

    // Routes pour les administrateurs uniquement
    Route::middleware('role:admin')->group(function () {
        Route::prefix('admin')->group(function () {
            // Gestion des utilisateurs (admin) - Endpoints spécifiés
            Route::post('users', [UserController::class, 'store']);
            Route::get('employees', [UserController::class, 'employees']);
            Route::post('employees', [UserController::class, 'store']); // Alias pour compatibilité
            Route::get('employees/{id}', [UserController::class, 'show']);
            Route::put('employees/{id}', [UserController::class, 'update']);
            Route::delete('employees/{id}', [UserController::class, 'destroy']);
            Route::get('users/admins', [UserController::class, 'admins']);

            // Gestion des sites (admin) - Endpoints spécifiés
            Route::post('sites', [SiteController::class, 'store']);

            // Assignations - Endpoints spécifiés
            Route::post('assign-site', [UserController::class, 'assignToSite']);
            Route::post('assignments', [UserController::class, 'assignToSite']); // Alias
            Route::delete('assignments/{userId}/{siteId}', [UserController::class, 'removeFromSite']);

            // Vérifications de localisation (admin)
            Route::post('verify-location/{id}', [LocationController::class, 'verifyLocation']);
            Route::post('request-verification', [LocationController::class, 'requestVerification']);

            // Rapports avancés - Endpoints spécifiés
            Route::get('pointages', [PointageController::class, 'index']); // Historique pointages
            Route::get('reports/attendance', [PointageController::class, 'attendanceReport']);
            Route::get('reports/productivity', [PointageController::class, 'productivityReport']);
            Route::get('logs/pointages', [PointageController::class, 'getPointageLogs']);
        });
    });
});

// Route de test de l'API
Route::get('/test', function () {
    return response()->json([
        'message' => 'ClockIn API is working!',
        'version' => '1.0.0',
        'timestamp' => now()->toISOString()
    ]);
});
