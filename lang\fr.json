{"auth": {"email": "Email", "password": "Mot de passe", "login": "Connexion", "logout": "Déconnexion", "failed": "Ces identifiants ne correspondent pas à nos enregistrements.", "throttle": "Trop de tentatives de connexion. Veuillez réessayer dans :seconds secondes.", "token_invalid": "<PERSON><PERSON> invalide", "token_expired": "Token expiré", "token_not_provided": "Token non fourni", "unauthorized": "Non autorisé", "login_success": "Connexion réussie", "logout_success": "Déconnexion réussie", "token_refreshed": "Token actualisé avec succès"}, "pointage": {"site": "Site", "start_latitude": "Latitude de début", "start_longitude": "Longitude de début", "end_latitude": "Latitude de fin", "end_longitude": "Longitude de fin", "started": "Pointage démarré avec succès", "ended": "Pointage terminé avec succès", "already_active": "Vous avez déjà un pointage en cours", "no_active": "Aucun pointage actif trouvé", "not_assigned": "Vous n'êtes pas assigné à ce site", "too_far": "Vous êtes trop loin du site de travail", "distance": "Distance", "max_distance": "Distance maximale autorisée"}, "location": {"site": "Site", "latitude": "Latitude", "longitude": "Longitude", "verified": "Localisation vérifiée avec succès", "failed": "Échec de la vérification de localisation", "in_range": "Dans la zone autorisée", "out_of_range": "Hors de la zone autorisée"}, "site": {"name": "Nom du site", "latitude": "Latitude", "longitude": "Longitude", "created": "Site créé avec succès", "updated": "Site mis à jour avec succès", "deleted": "Site supprimé avec succès", "not_found": "Site non trouvé", "has_pointages": "Impossible de supprimer le site. Des pointages y sont associés."}, "user": {"name": "Nom", "email": "Email", "role": "R<PERSON><PERSON>", "hire_date": "Date d'embauche", "cnss_number": "Numéro CNSS", "created": "Utilisateur c<PERSON>é avec succès", "updated": "Utilisateur mis à jour avec succès", "deleted": "Utilisateur supprimé avec succès", "not_found": "Utilisateur non trouvé", "cannot_delete_self": "Vous ne pouvez pas supprimer votre propre compte", "has_pointages": "Impossible de supprimer l'utilisateur. Des pointages lui sont associés."}, "assignment": {"created": "Assignation créée avec succès", "deleted": "Assignation supprimée avec succès", "already_exists": "L'utilisateur est déjà assigné à ce site", "not_found": "Assignation non trouvée"}, "verification": {"requested": "De<PERSON>e de vérification envoyée", "approved": "Vérification approuvée", "rejected": "Vérification rejetée", "pending": "En attente", "verified": "Vérifié", "failed": "Échec"}, "validation": {"failed": "Les données fournies ne sont pas valides", "required": "Le champ :attribute est obligatoire", "email": "Le champ :attribute doit être une adresse email valide", "string": "Le champ :attribute doit être une chaîne de caractères", "numeric": "Le champ :attribute doit être un nombre", "between": {"numeric": "Le champ :attribute doit être entre :min et :max"}, "min": {"string": "Le champ :attribute doit contenir au moins :min caractères"}, "max": {"string": "Le champ :attribute ne peut pas dépasser :max caractères"}, "exists": "Le :attribute sélectionné n'existe pas", "unique": "Le :attribute a déjà été pris"}, "errors": {"access_denied": "<PERSON><PERSON>ès refusé", "admin_required": "Droits administrateur requis", "server_error": "<PERSON><PERSON>ur serveur interne", "not_found": "Ressource non trouvée", "method_not_allowed": "Méthode non autorisée", "too_many_requests": "Trop de requêtes"}, "success": {"operation_completed": "Opération terminée avec succès", "data_saved": "Données sauvegardées avec succès", "data_updated": "<PERSON>n<PERSON> mises à jour avec succès", "data_deleted": "Données supprimées avec succès"}}