{"auth": {"email": "الب<PERSON>يد الإلكتروني", "password": "كلمة المرور", "login": "تسجيل الدخول", "logout": "تسجيل الخروج", "failed": "بيانات الاعتماد هذه غير متطابقة مع سجلاتنا.", "throttle": "محاولات تسجيل دخول كثيرة جداً. يرجى المحاولة مرة أخرى خلال :seconds ثانية.", "token_invalid": "رمز غير صالح", "token_expired": "انتهت صلاحية الرمز", "token_not_provided": "لم يتم توفير الرمز", "unauthorized": "<PERSON>ير مخول", "login_success": "تم تسجيل الدخول بنجاح", "logout_success": "تم تسجيل الخروج بنجاح", "token_refreshed": "تم تحديث الرمز بنجاح"}, "pointage": {"site": "الموقع", "start_latitude": "خط العرض للبداية", "start_longitude": "<PERSON>ط الطول للبداية", "end_latitude": "خط العرض للنهاية", "end_longitude": "<PERSON>ط الطول للنهاية", "started": "تم بدء التوقيت بنجاح", "ended": "تم إنهاء التوقيت بنجاح", "already_active": "لديك توقيت نشط بالفعل", "no_active": "لم يتم العثور على توقيت نشط", "not_assigned": "لم يتم تعيينك لهذا الموقع", "too_far": "أنت بعيد جداً عن موقع العمل", "distance": "المسافة", "max_distance": "أقصى مسافة مسموحة"}, "location": {"site": "الموقع", "latitude": "<PERSON><PERSON> العرض", "longitude": "<PERSON><PERSON> الطول", "verified": "تم التحقق من الموقع بنجاح", "failed": "فشل في التحقق من الموقع", "in_range": "<PERSON><PERSON>ن المنطقة المسموحة", "out_of_range": "<PERSON>ارج المنطقة المسموحة"}, "site": {"name": "اسم الموقع", "latitude": "<PERSON><PERSON> العرض", "longitude": "<PERSON><PERSON> الطول", "created": "تم إنشاء الموقع بنجاح", "updated": "تم تحديث الموقع بنجاح", "deleted": "تم حذف الموقع بنجاح", "not_found": "الموقع غير موجود", "has_pointages": "لا يمكن حذف الموقع. هناك توقيتات مرتبطة به."}, "user": {"name": "الاسم", "email": "الب<PERSON>يد الإلكتروني", "role": "الدور", "hire_date": "تاريخ التوظيف", "cnss_number": "رقم الضمان الاجتماعي", "created": "تم إنشاء المستخدم بنجاح", "updated": "تم تحديث المستخدم بنجاح", "deleted": "تم حذف المستخدم بنجاح", "not_found": "المستخدم غير موجود", "cannot_delete_self": "لا يمكنك حذف حسابك الخاص", "has_pointages": "لا يمكن حذف المستخدم. هناك توقيتات مرتبطة به."}, "assignment": {"created": "تم إنشاء التعيين بنجاح", "deleted": "تم حذف التعيين بنجاح", "already_exists": "المستخدم معين بالفعل لهذا الموقع", "not_found": "التعيين غير موجود"}, "verification": {"requested": "تم إرسال طلب التحقق", "approved": "تم الموافقة على التحقق", "rejected": "تم رفض التحقق", "pending": "في الانتظار", "verified": "تم التحقق", "failed": "فشل"}, "validation": {"failed": "البيانات المقدمة غير صالحة", "required": "حقل :attribute مطلوب", "email": "حقل :attribute يجب أن يكون عنوان بريد إلكتروني صالح", "string": "حقل :attribute يجب أن يكون نص", "numeric": "حقل :attribute يجب أن يكون رقم", "between": {"numeric": "حقل :attribute يجب أن يكون بين :min و :max"}, "min": {"string": "حقل :attribute يجب أن يحتوي على الأقل :min أحرف"}, "max": {"string": "حقل :attribute لا يمكن أن يتجاوز :max أحرف"}, "exists": ":attribute المحدد غير موجود", "unique": ":attribute مستخدم بالفعل"}, "errors": {"access_denied": "تم رفض الوصول", "admin_required": "مطلوب صلاحيات المدير", "server_error": "خطأ داخلي في الخادم", "not_found": "المورد غير موجود", "method_not_allowed": "الطريقة غير مسموحة", "too_many_requests": "طلبات كثيرة جداً"}, "success": {"operation_completed": "تمت العملية بنجاح", "data_saved": "تم حفظ البيانات بنجاح", "data_updated": "تم تحديث البيانات بنجاح", "data_deleted": "تم حذف البيانات بنجاح"}}