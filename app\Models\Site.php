<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Site extends Model
{
    use HasFactory;

    protected $primaryKey = 'id_site';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'nom_site',
        'latitude',
        'longitude',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'latitude' => 'decimal:8',
            'longitude' => 'decimal:8',
        ];
    }

    /**
     * Relations
     */
    public function assignments(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Assignment::class, 'id_site', 'id_site');
    }

    public function pointages(): HasMany
    {
        return $this->hasMany(Pointage::class, 'id_site', 'id_site');
    }

    public function verifications(): HasMany
    {
        return $this->hasMany(Verification::class, 'id_site', 'id_site');
    }

    public function logsPointages(): Has<PERSON>any
    {
        return $this->hasMany(PointageLog::class, 'id_site', 'id_site');
    }

    /**
     * Calculer la distance entre deux points GPS (formule de Haversine)
     *
     * @param float $lat1
     * @param float $lon1
     * @param float $lat2
     * @param float $lon2
     * @return float Distance en mètres
     */
    public static function calculateDistance($lat1, $lon1, $lat2, $lon2): float
    {
        $earthRadius = 6371000; // Rayon de la Terre en mètres

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
            cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
            sin($dLon / 2) * sin($dLon / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Vérifier si une position est dans le rayon autorisé (50m)
     *
     * @param float $userLat
     * @param float $userLon
     * @param int $radius
     * @return bool
     */
    public function isWithinRadius($userLat, $userLon, $radius = 50): bool
    {
        $distance = self::calculateDistance(
            $this->latitude,
            $this->longitude,
            $userLat,
            $userLon
        );

        return $distance <= $radius;
    }
}
