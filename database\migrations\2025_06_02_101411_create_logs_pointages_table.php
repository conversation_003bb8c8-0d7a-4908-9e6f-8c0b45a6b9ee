<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('logs_pointages', function (Blueprint $table) {
            $table->id('id_log');
            $table->unsignedBigInteger('id_user');
            $table->unsignedBigInteger('id_site');
            $table->decimal('latitude', 10, 8);
            $table->decimal('longitude', 11, 8);
            $table->dateTime('date_tentative');
            $table->boolean('succes');
            $table->text('raison_echec')->nullable();
            $table->timestamp('created_at')->useCurrent();

            // Clés étrangères avec CASCADE
            $table->foreign('id_user')->references('id_user')->on('users')->onDelete('cascade');
            $table->foreign('id_site')->references('id_site')->on('sites')->onDelete('cascade');

            // Index pour optimiser les performances
            $table->index('id_user');
            $table->index('id_site');
            $table->index('date_tentative');
            $table->index('succes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('logs_pointages');
    }
};
