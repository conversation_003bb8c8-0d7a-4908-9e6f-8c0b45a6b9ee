<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\QueryException;
use PDO;
use PDOException;

class TestDatabaseConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:test-connection {--create-db : Créer la base de données si elle n\'existe pas}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Tester la connexion à la base de données MySQL et créer la base si nécessaire';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Test de connexion à la base de données...');
        $this->newLine();

        // Afficher les paramètres de connexion
        $this->displayConnectionInfo();

        // Test de connexion MySQL (sans base de données spécifique)
        if (!$this->testMySQLConnection()) {
            return Command::FAILURE;
        }

        // Créer la base de données si demandé
        if ($this->option('create-db')) {
            if (!$this->createDatabaseIfNotExists()) {
                return Command::FAILURE;
            }
        }

        // Test de connexion à la base de données spécifique
        if (!$this->testDatabaseConnection()) {
            return Command::FAILURE;
        }

        $this->info('🎉 Tous les tests de connexion ont réussi!');
        $this->info('Vous pouvez maintenant exécuter les migrations avec: php artisan migrate');

        return Command::SUCCESS;
    }

    private function displayConnectionInfo()
    {
        $this->info('Paramètres de connexion:');
        $this->line('- Hôte: ' . config('database.connections.mysql.host'));
        $this->line('- Port: ' . config('database.connections.mysql.port'));
        $this->line('- Base de données: ' . config('database.connections.mysql.database'));
        $this->line('- Utilisateur: ' . config('database.connections.mysql.username'));
        $this->line('- Charset: ' . config('database.connections.mysql.charset'));
        $this->line('- Collation: ' . config('database.connections.mysql.collation'));
        $this->line('- Engine: ' . config('database.connections.mysql.engine'));
        $this->newLine();
    }

    private function testMySQLConnection(): bool
    {
        $this->info('📡 Test de connexion MySQL...');

        try {
            $config = config('database.connections.mysql');

            $pdo = new PDO(
                "mysql:host={$config['host']};port={$config['port']};charset={$config['charset']}",
                $config['username'],
                $config['password'],
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_TIMEOUT => 10,
                ]
            );

            $this->info('✅ Connexion MySQL réussie!');
            $this->newLine();
            return true;

        } catch (PDOException $e) {
            $this->error('❌ Erreur de connexion MySQL: ' . $e->getMessage());
            $this->displayTroubleshooting();
            return false;
        }
    }

    private function createDatabaseIfNotExists(): bool
    {
        $this->info('🔍 Vérification/création de la base de données...');

        try {
            $config = config('database.connections.mysql');
            $database = $config['database'];

            $pdo = new PDO(
                "mysql:host={$config['host']};port={$config['port']};charset={$config['charset']}",
                $config['username'],
                $config['password'],
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );

            // Vérifier si la base existe
            $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
            $stmt->execute([$database]);

            if ($stmt->fetch()) {
                $this->info("✅ La base de données '{$database}' existe déjà.");
            } else {
                // Créer la base de données
                $sql = "CREATE DATABASE `{$database}`
                        CHARACTER SET {$config['charset']}
                        COLLATE {$config['collation']}";

                $pdo->exec($sql);
                $this->info("✅ Base de données '{$database}' créée avec succès!");
            }

            $this->newLine();
            return true;

        } catch (PDOException $e) {
            $this->error('❌ Erreur lors de la création de la base: ' . $e->getMessage());
            return false;
        }
    }

    private function testDatabaseConnection(): bool
    {
        $this->info('🔗 Test de connexion à la base de données...');

        try {
            // Test avec Laravel DB
            $result = DB::select('SELECT 1 as test');

            if ($result && $result[0]->test == 1) {
                $this->info('✅ Connexion à la base de données réussie!');

                // Afficher des informations supplémentaires
                $version = DB::select('SELECT VERSION() as version')[0]->version;
                $this->line("Version MySQL: {$version}");

                $this->newLine();
                return true;
            }

        } catch (QueryException $e) {
            $this->error('❌ Erreur de connexion à la base: ' . $e->getMessage());

            if (str_contains($e->getMessage(), 'Unknown database')) {
                $this->warn('💡 La base de données n\'existe pas. Utilisez --create-db pour la créer automatiquement.');
            }

            return false;
        }

        return false;
    }

    private function displayTroubleshooting()
    {
        $this->newLine();
        $this->warn('🔧 GUIDE DE DÉPANNAGE:');
        $this->line('==========================================');
        $this->newLine();

        $this->line('1. Vérifiez que WampServer est démarré:');
        $this->line('   - L\'icône WampServer doit être verte');
        $this->line('   - Apache et MySQL doivent être en cours d\'exécution');
        $this->newLine();

        $this->line('2. Vérifiez les paramètres dans le fichier .env');
        $this->newLine();

        $this->line('3. Erreurs courantes:');
        $this->line('   - "Connection refused": MySQL n\'est pas démarré');
        $this->line('   - "Access denied": Mauvais utilisateur/mot de passe');
        $this->line('   - "Unknown database": Utilisez --create-db');
        $this->newLine();

        $this->line('4. Testez manuellement:');
        $this->line('   - Accédez à http://localhost:8080/phpmyadmin');
        $this->line('   - Vérifiez la connexion avec les mêmes identifiants');
        $this->newLine();
    }
}
