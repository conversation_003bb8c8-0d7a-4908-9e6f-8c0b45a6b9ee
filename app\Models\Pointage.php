<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Pointage extends Model
{
    use HasFactory;

    protected $primaryKey = 'id_pointage';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'id_user',
        'id_site',
        'debut_pointage',
        'fin_pointage',
        'duree',
        'debut_latitude',
        'debut_longitude',
        'fin_latitude',
        'fin_longitude',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'debut_pointage' => 'datetime',
            'fin_pointage' => 'datetime',
            'debut_latitude' => 'decimal:8',
            'debut_longitude' => 'decimal:8',
            'fin_latitude' => 'decimal:8',
            'fin_longitude' => 'decimal:8',
        ];
    }

    /**
     * Relations
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'id_user', 'id_user');
    }

    public function site(): BelongsTo
    {
        return $this->belongsTo(Site::class, 'id_site', 'id_site');
    }

    /**
     * Calculer et mettre à jour la durée du pointage
     */
    public function calculateDuration(): void
    {
        if ($this->debut_pointage && $this->fin_pointage) {
            $debut = Carbon::parse($this->debut_pointage);
            $fin = Carbon::parse($this->fin_pointage);

            $diffInSeconds = $fin->diffInSeconds($debut);
            $hours = intval($diffInSeconds / 3600);
            $minutes = intval(($diffInSeconds % 3600) / 60);
            $seconds = $diffInSeconds % 60;

            $this->duree = sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
        }
    }

    /**
     * Scope pour filtrer par utilisateur
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('id_user', $userId);
    }

    /**
     * Scope pour filtrer par site
     */
    public function scopeForSite($query, $siteId)
    {
        return $query->where('id_site', $siteId);
    }

    /**
     * Scope pour filtrer par période
     */
    public function scopeForPeriod($query, $dateDebut, $dateFin)
    {
        return $query->whereBetween('debut_pointage', [$dateDebut, $dateFin]);
    }

    /**
     * Scope pour les pointages en cours (non terminés)
     */
    public function scopeInProgress($query)
    {
        return $query->whereNull('fin_pointage');
    }

    /**
     * Scope pour les pointages terminés
     */
    public function scopeCompleted($query)
    {
        return $query->whereNotNull('fin_pointage');
    }

    /**
     * Vérifier si l'utilisateur a un pointage en cours
     */
    public static function hasActivePointage($userId, $siteId): bool
    {
        return self::forUser($userId)
            ->forSite($siteId)
            ->inProgress()
            ->exists();
    }

    /**
     * Obtenir le pointage actif d'un utilisateur sur un site
     */
    public static function getActivePointage($userId, $siteId): ?self
    {
        return self::forUser($userId)
            ->forSite($siteId)
            ->inProgress()
            ->first();
    }
}
