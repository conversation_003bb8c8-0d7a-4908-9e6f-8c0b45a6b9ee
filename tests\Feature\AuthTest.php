<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AuthTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Créer un utilisateur de test
        $this->user = User::create([
            'nom' => 'Test User',
            'email' => '<EMAIL>',
            'mot_de_passe' => Hash::make('password123'),
            'role' => 'employee',
        ]);
    }

    /**
     * Test de connexion réussie
     */
    public function test_successful_login(): void
    {
        $response = $this->postJson('/api/auth/login', [
            'email' => '<EMAIL>',
            'mot_de_passe' => 'password123',
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'user' => [
                        'id_user',
                        'nom',
                        'email',
                        'role',
                    ],
                    'authorization' => [
                        'token',
                        'type',
                        'expires_in',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                ]);
    }

    /**
     * Test de connexion avec mauvais mot de passe
     */
    public function test_login_with_wrong_password(): void
    {
        $response = $this->postJson('/api/auth/login', [
            'email' => '<EMAIL>',
            'mot_de_passe' => 'wrongpassword',
        ]);

        $response->assertStatus(401)
                ->assertJson([
                    'success' => false,
                ]);
    }
}
