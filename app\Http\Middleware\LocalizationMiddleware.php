<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Symfony\Component\HttpFoundation\Response;

class LocalizationMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Langues supportées
        $supportedLocales = ['fr', 'ar'];
        
        // Récupérer la langue depuis l'en-tête Accept-Language
        $locale = $request->header('Accept-Language', 'fr');
        
        // Nettoyer la locale (prendre seulement les 2 premiers caractères)
        $locale = substr($locale, 0, 2);
        
        // Vérifier si la langue est supportée
        if (!in_array($locale, $supportedLocales)) {
            $locale = 'fr'; // Langue par défaut
        }
        
        // Définir la locale pour l'application
        App::setLocale($locale);
        
        return $next($request);
    }
}
