<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PointageLog extends Model
{
    use HasFactory;

    protected $table = 'logs_pointages';
    protected $primaryKey = 'id_log';

    // Selon la structure cible: seulement created_at, pas updated_at
    const UPDATED_AT = null;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'id_user',
        'id_site',
        'latitude',
        'longitude',
        'date_tentative',
        'succes',
        'raison_echec',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'latitude' => 'decimal:8',
            'longitude' => 'decimal:8',
            'date_tentative' => 'datetime',
            'succes' => 'boolean',
            'created_at' => 'datetime',
        ];
    }

    /**
     * Relations
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'id_user', 'id_user');
    }

    public function site(): BelongsTo
    {
        return $this->belongsTo(Site::class, 'id_site', 'id_site');
    }

    /**
     * Scopes
     */
    public function scopeSuccessful($query)
    {
        return $query->where('succes', true);
    }

    public function scopeFailed($query)
    {
        return $query->where('succes', false);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('id_user', $userId);
    }

    public function scopeForSite($query, $siteId)
    {
        return $query->where('id_site', $siteId);
    }

    public function scopeForPeriod($query, $dateDebut, $dateFin)
    {
        return $query->whereBetween('date_tentative', [$dateDebut, $dateFin]);
    }

    /**
     * Créer un log de tentative de pointage
     */
    public static function logAttempt($userId, $siteId, $latitude, $longitude, $success, $reason = null): self
    {
        return self::create([
            'id_user' => $userId,
            'id_site' => $siteId,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'date_tentative' => now(),
            'succes' => $success,
            'raison_echec' => $reason,
        ]);
    }
}
