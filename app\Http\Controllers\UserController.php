<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
use App\Models\Assignment;
use App\Models\Pointage;
use App\Models\Site;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::guard('api')->user();

        // Seuls les admins peuvent voir tous les utilisateurs
        if ($user->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Accès refusé. Droits administrateur requis.'
            ], 403);
        }

        $query = User::query();

        // Filtres optionnels
        if ($request->has('role')) {
            $query->where('role', $request->role);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nom', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('numero_cnss', 'like', "%{$search}%");
            });
        }

        // Inclure les relations si demandé
        if ($request->has('with_assignments')) {
            $query->with('assignments.site');
        }

        $users = $query->orderBy('nom')->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $users
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $user = Auth::guard('api')->user();

        // Seuls les admins peuvent créer des utilisateurs
        if ($user->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Accès refusé. Droits administrateur requis.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'nom' => 'required|string|between:2,100',
            'email' => 'required|string|email|max:100|unique:users',
            'mot_de_passe' => 'required|string|min:6',
            'role' => 'required|in:admin,employee',
            'date_embauche' => 'sometimes|date',
            'numero_cnss' => 'sometimes|string|max:50',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Données de validation invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        $newUser = User::create([
            'nom' => $request->nom,
            'email' => $request->email,
            'mot_de_passe' => Hash::make($request->mot_de_passe),
            'role' => $request->role,
            'date_embauche' => $request->date_embauche,
            'numero_cnss' => $request->numero_cnss,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Utilisateur créé avec succès',
            'data' => $newUser
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        $currentUser = Auth::guard('api')->user();

        // Les utilisateurs ne peuvent voir que leur propre profil, sauf les admins
        if ($currentUser->role !== 'admin' && $currentUser->id_user != $id) {
            return response()->json([
                'success' => false,
                'message' => 'Accès refusé'
            ], 403);
        }

        $user = User::with(['assignments.site', 'pointages.site'])->find($id);

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Utilisateur non trouvé'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $user
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $currentUser = Auth::guard('api')->user();

        // Les utilisateurs ne peuvent modifier que leur propre profil, sauf les admins
        if ($currentUser->role !== 'admin' && $currentUser->id_user != $id) {
            return response()->json([
                'success' => false,
                'message' => 'Accès refusé'
            ], 403);
        }

        $user = User::find($id);

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Utilisateur non trouvé'
            ], 404);
        }

        $rules = [
            'nom' => 'sometimes|string|between:2,100',
            'email' => 'sometimes|string|email|max:100|unique:users,email,' . $id . ',id_user',
            'date_embauche' => 'sometimes|date',
            'numero_cnss' => 'sometimes|string|max:50',
        ];

        // Seuls les admins peuvent modifier le rôle et le mot de passe
        if ($currentUser->role === 'admin') {
            $rules['role'] = 'sometimes|in:admin,employee';
            $rules['mot_de_passe'] = 'sometimes|string|min:6';
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Données de validation invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        $updateData = $request->only(['nom', 'email', 'date_embauche', 'numero_cnss']);

        if ($currentUser->role === 'admin') {
            if ($request->has('role')) {
                $updateData['role'] = $request->role;
            }
            if ($request->has('mot_de_passe')) {
                $updateData['mot_de_passe'] = Hash::make($request->mot_de_passe);
            }
        }

        $user->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'Utilisateur mis à jour avec succès',
            'data' => $user
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        $currentUser = Auth::guard('api')->user();

        // Seuls les admins peuvent supprimer des utilisateurs
        if ($currentUser->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Accès refusé. Droits administrateur requis.'
            ], 403);
        }

        $user = User::find($id);

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Utilisateur non trouvé'
            ], 404);
        }

        // Empêcher la suppression de son propre compte
        if ($currentUser->id_user == $id) {
            return response()->json([
                'success' => false,
                'message' => 'Vous ne pouvez pas supprimer votre propre compte'
            ], 400);
        }

        // Vérifier s'il y a des pointages associés
        $hasPointages = Pointage::where('id_user', $id)->exists();
        if ($hasPointages) {
            return response()->json([
                'success' => false,
                'message' => 'Impossible de supprimer l\'utilisateur. Des pointages lui sont associés.'
            ], 400);
        }

        // Supprimer les assignations associées
        Assignment::where('id_user', $id)->delete();

        $user->delete();

        return response()->json([
            'success' => true,
            'message' => 'Utilisateur supprimé avec succès'
        ]);
    }

    /**
     * Obtenir les pointages d'un utilisateur
     */
    public function pointages(Request $request, string $id): JsonResponse
    {
        $currentUser = Auth::guard('api')->user();

        // Les utilisateurs ne peuvent voir que leurs propres pointages, sauf les admins
        if ($currentUser->role !== 'admin' && $currentUser->id_user != $id) {
            return response()->json([
                'success' => false,
                'message' => 'Accès refusé'
            ], 403);
        }

        $user = User::find($id);

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Utilisateur non trouvé'
            ], 404);
        }

        $query = Pointage::where('id_user', $id)->with('site');

        // Filtres optionnels
        if ($request->has('date_debut')) {
            $query->whereDate('debut_pointage', '>=', $request->date_debut);
        }

        if ($request->has('date_fin')) {
            $query->whereDate('debut_pointage', '<=', $request->date_fin);
        }

        if ($request->has('site_id')) {
            $query->where('id_site', $request->site_id);
        }

        $pointages = $query->orderBy('debut_pointage', 'desc')
                          ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'user' => $user,
            'pointages' => $pointages
        ]);
    }

    /**
     * Obtenir la liste des employés
     */
    public function employees(): JsonResponse
    {
        $currentUser = Auth::guard('api')->user();

        if ($currentUser->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Accès refusé. Droits administrateur requis.'
            ], 403);
        }

        $employees = User::employees()->with('assignments.site')->get();

        return response()->json([
            'success' => true,
            'data' => $employees
        ]);
    }

    /**
     * Obtenir la liste des administrateurs
     */
    public function admins(): JsonResponse
    {
        $currentUser = Auth::guard('api')->user();

        if ($currentUser->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Accès refusé. Droits administrateur requis.'
            ], 403);
        }

        $admins = User::admins()->get();

        return response()->json([
            'success' => true,
            'data' => $admins
        ]);
    }

    /**
     * Assigner un utilisateur à un site
     */
    public function assignToSite(Request $request): JsonResponse
    {
        $currentUser = Auth::guard('api')->user();

        if ($currentUser->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Accès refusé. Droits administrateur requis.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'id_user' => 'required|exists:users,id_user',
            'id_site' => 'required|exists:sites,id_site',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Données de validation invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        // Vérifier si l'assignation existe déjà
        $existingAssignment = Assignment::where('id_user', $request->id_user)
                                       ->where('id_site', $request->id_site)
                                       ->first();

        if ($existingAssignment) {
            return response()->json([
                'success' => false,
                'message' => 'L\'utilisateur est déjà assigné à ce site'
            ], 400);
        }

        $assignment = Assignment::create([
            'id_user' => $request->id_user,
            'id_site' => $request->id_site,
            'date_assignation' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Assignation créée avec succès',
            'data' => $assignment->load(['user', 'site'])
        ], 201);
    }

    /**
     * Retirer un utilisateur d'un site
     */
    public function removeFromSite(string $userId, string $siteId): JsonResponse
    {
        $currentUser = Auth::guard('api')->user();

        if ($currentUser->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Accès refusé. Droits administrateur requis.'
            ], 403);
        }

        $assignment = Assignment::where('id_user', $userId)
                               ->where('id_site', $siteId)
                               ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'Assignation non trouvée'
            ], 404);
        }

        $assignment->delete();

        return response()->json([
            'success' => true,
            'message' => 'Assignation supprimée avec succès'
        ]);
    }
}
