<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SiteSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sites = [
            [
                'nom_site' => 'Chantier Centre-Ville',
                'latitude' => 33.5731,
                'longitude' => -7.5898,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nom_site' => 'Projet Résidentiel Nord',
                'latitude' => 33.5892,
                'longitude' => -7.6036,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nom_site' => 'Construction Industrielle Est',
                'latitude' => 33.5650,
                'longitude' => -7.5700,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nom_site' => 'Rénovation Quartier Historique',
                'latitude' => 33.5720,
                'longitude' => -7.5950,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($sites as $site) {
            \App\Models\Site::create($site);
        }
    }
}
