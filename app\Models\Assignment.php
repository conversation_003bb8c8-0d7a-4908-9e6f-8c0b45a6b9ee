<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Assignment extends Model
{
    use HasFactory;

    protected $primaryKey = 'id_assignment';

    // Selon la structure cible: seulement created_at, pas updated_at
    const UPDATED_AT = null;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'id_user',
        'id_site',
        'date_assignation',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'date_assignation' => 'date',
            'created_at' => 'datetime',
        ];
    }

    /**
     * Relations
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'id_user', 'id_user');
    }

    public function site(): BelongsTo
    {
        return $this->belongsTo(Site::class, 'id_site', 'id_site');
    }

    /**
     * Scope pour vérifier si un utilisateur est assigné à un site
     */
    public function scopeForUserAndSite($query, $userId, $siteId)
    {
        return $query->where('id_user', $userId)->where('id_site', $siteId);
    }

    /**
     * Vérifier si un utilisateur est assigné à un site
     */
    public static function isUserAssignedToSite($userId, $siteId): bool
    {
        return self::forUserAndSite($userId, $siteId)->exists();
    }
}
