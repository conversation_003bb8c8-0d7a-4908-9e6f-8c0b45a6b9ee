{"auth": {"login_success": "Connexion réussie", "login_failed": "Identifiants invalides", "logout_success": "Déconnexion réussie", "unauthorized": "Non autorisé", "token_expired": "Token expiré", "token_invalid": "<PERSON><PERSON> invalide"}, "pointage": {"location_verified": "Localisation vérifiée", "location_out_of_range": "Hors du rayon de 50m", "pointage_saved": "Pointage enregistré", "pointage_start_success": "<PERSON>ébut de pointage enregistré", "pointage_end_success": "Fin de pointage enregistrée", "pointage_already_active": "Un pointage est déjà en cours", "pointage_not_found": "Aucun pointage actif trouvé", "verification_saved": "Vérification enregistrée", "verification_failed": "Vé<PERSON>", "verification_unauthorized": "Vérification non autorisée"}, "site": {"site_created": "Chantier ajouté avec succès", "site_assigned": "Chantier assigné avec succès", "site_not_found": "Chantier non trouvé", "assignment_not_found": "Assignation non trouvée"}, "employee": {"employee_created": "Employé a<PERSON>té avec succès", "employee_updated": "Employé mis à jour avec succès", "employee_deleted": "Employé supprimé avec succès", "employee_not_found": "Employé non trouvé", "email_already_exists": "Cette adresse email est déjà utilisée"}, "verification": {"request_sent": "De<PERSON>e de vérification envoyée", "verification_completed": "Vérification terminée"}, "validation": {"required": "Ce champ est requis", "email": "Format d'email invalide", "min": "Minimum :min caractères requis", "max": "Maximum :max caractères autorisés", "numeric": "Doit être un nombre", "exists": "La valeur sélectionnée n'existe pas", "unique": "Cette valeur est déjà utilisée", "in": "La valeur sélectionnée est invalide", "latitude": "Latitude invalide (doit être entre -90 et 90)", "longitude": "Longitude invalide (doit être entre -180 et 180)"}, "errors": {"server_error": "<PERSON><PERSON>ur serveur interne", "not_found": "Ressource non trouvée", "forbidden": "Accès interdit", "bad_request": "<PERSON><PERSON><PERSON><PERSON><PERSON> invalide", "validation_failed": "Erreur de validation", "database_error": "Erreur de base de données"}, "general": {"success": "Su<PERSON>ès", "error": "<PERSON><PERSON><PERSON>", "warning": "Attention", "info": "Information", "created": "<PERSON><PERSON><PERSON>", "updated": "Mis à jour", "deleted": "Supprimé"}}