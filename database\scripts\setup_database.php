<?php

/**
 * Script de configuration de la base de données ClockIn
 * Ce script vérifie la connexion MySQL et crée la base de données si nécessaire
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Database\QueryException;

class DatabaseSetup
{
    private $host;
    private $port;
    private $username;
    private $password;
    private $database;
    private $charset;
    private $collation;

    public function __construct()
    {
        // Charger les variables d'environnement
        $this->loadEnvVariables();
        
        $this->host = $_ENV['DB_HOST'] ?? '127.0.0.1';
        $this->port = $_ENV['DB_PORT'] ?? '3306';
        $this->username = $_ENV['DB_USERNAME'] ?? 'root';
        $this->password = $_ENV['DB_PASSWORD'] ?? '';
        $this->database = $_ENV['DB_DATABASE'] ?? 'clockin';
        $this->charset = $_ENV['DB_CHARSET'] ?? 'utf8mb4';
        $this->collation = $_ENV['DB_COLLATION'] ?? 'utf8mb4_unicode_ci';
    }

    private function loadEnvVariables()
    {
        $envFile = __DIR__ . '/../../.env';
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
                    list($key, $value) = explode('=', $line, 2);
                    $_ENV[trim($key)] = trim($value);
                }
            }
        }
    }

    public function testConnection()
    {
        echo "🔍 Test de connexion à MySQL...\n";
        echo "Hôte: {$this->host}:{$this->port}\n";
        echo "Utilisateur: {$this->username}\n";
        echo "Base de données: {$this->database}\n\n";

        try {
            // Test de connexion sans spécifier de base de données
            $pdo = new PDO(
                "mysql:host={$this->host};port={$this->port};charset={$this->charset}",
                $this->username,
                $this->password,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset} COLLATE {$this->collation}"
                ]
            );

            echo "✅ Connexion MySQL réussie!\n\n";
            return $pdo;

        } catch (PDOException $e) {
            echo "❌ Erreur de connexion MySQL: " . $e->getMessage() . "\n";
            $this->displayTroubleshooting();
            return false;
        }
    }

    public function createDatabase($pdo)
    {
        echo "🔍 Vérification de l'existence de la base de données '{$this->database}'...\n";

        try {
            // Vérifier si la base de données existe
            $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
            $stmt->execute([$this->database]);
            
            if ($stmt->fetch()) {
                echo "✅ La base de données '{$this->database}' existe déjà.\n\n";
                return true;
            }

            // Créer la base de données
            echo "📝 Création de la base de données '{$this->database}'...\n";
            $sql = "CREATE DATABASE `{$this->database}` 
                    CHARACTER SET {$this->charset} 
                    COLLATE {$this->collation}";
            
            $pdo->exec($sql);
            echo "✅ Base de données '{$this->database}' créée avec succès!\n\n";
            return true;

        } catch (PDOException $e) {
            echo "❌ Erreur lors de la création de la base de données: " . $e->getMessage() . "\n";
            return false;
        }
    }

    public function testDatabaseConnection()
    {
        echo "🔍 Test de connexion à la base de données '{$this->database}'...\n";

        try {
            $pdo = new PDO(
                "mysql:host={$this->host};port={$this->port};dbname={$this->database};charset={$this->charset}",
                $this->username,
                $this->password,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                ]
            );

            // Test simple
            $stmt = $pdo->query("SELECT 1 as test");
            $result = $stmt->fetch();
            
            if ($result['test'] == 1) {
                echo "✅ Connexion à la base de données '{$this->database}' réussie!\n\n";
                return true;
            }

        } catch (PDOException $e) {
            echo "❌ Erreur de connexion à la base de données: " . $e->getMessage() . "\n";
            return false;
        }
    }

    public function displayTroubleshooting()
    {
        echo "\n🔧 GUIDE DE DÉPANNAGE:\n";
        echo "==========================================\n\n";
        
        echo "1. Vérifiez que WampServer est démarré:\n";
        echo "   - L'icône WampServer doit être verte\n";
        echo "   - Apache et MySQL doivent être en cours d'exécution\n\n";
        
        echo "2. Vérifiez les paramètres de connexion:\n";
        echo "   - Hôte: {$this->host}\n";
        echo "   - Port: {$this->port}\n";
        echo "   - Utilisateur: {$this->username}\n";
        echo "   - Mot de passe: " . (empty($this->password) ? '(vide)' : '(défini)') . "\n\n";
        
        echo "3. Erreurs courantes:\n";
        echo "   - 'Connection refused': MySQL n'est pas démarré\n";
        echo "   - 'Access denied': Mauvais utilisateur/mot de passe\n";
        echo "   - 'Unknown database': La base n'existe pas (sera créée automatiquement)\n\n";
        
        echo "4. Vérifications supplémentaires:\n";
        echo "   - Accédez à http://localhost:8080/phpmyadmin\n";
        echo "   - Vérifiez que vous pouvez vous connecter avec les mêmes identifiants\n";
        echo "   - Vérifiez que le port 3306 n'est pas bloqué par un firewall\n\n";
    }

    public function run()
    {
        echo "🚀 CONFIGURATION DE LA BASE DE DONNÉES CLOCKIN\n";
        echo "===============================================\n\n";

        // Test de connexion MySQL
        $pdo = $this->testConnection();
        if (!$pdo) {
            return false;
        }

        // Création de la base de données si nécessaire
        if (!$this->createDatabase($pdo)) {
            return false;
        }

        // Test de connexion à la base de données
        if (!$this->testDatabaseConnection()) {
            return false;
        }

        echo "🎉 Configuration terminée avec succès!\n";
        echo "Vous pouvez maintenant exécuter: php artisan migrate\n\n";
        return true;
    }
}

// Exécution du script
if (php_sapi_name() === 'cli') {
    $setup = new DatabaseSetup();
    $success = $setup->run();
    exit($success ? 0 : 1);
}
