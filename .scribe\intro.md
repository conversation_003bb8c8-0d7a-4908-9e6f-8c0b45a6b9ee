# Introduction

API Laravel 11 pour la gestion de pointages d'employés avec géolocalisation précise (rayon 50m), authentification JWT, et calcul automatique de durée via triggers MySQL. Structure conforme avec 6 tables exactes selon spécifications.

<aside>
    <strong>Base URL</strong>: <code>http://localhost</code>
</aside>

    ## 🎯 Fonctionnalités Principales

    - **Géolocalisation précise** : Validation rayon 50m avec calcul Haversine
    - **Trigger MySQL automatique** : Calcul de durée via `TIMEDIFF(fin_pointage, debut_pointage)`
    - **Authentification JWT** : Tokens sécurisés avec expiration 1h
    - **Support bilingue** : Français/Arabe via header `Accept-Language`
    - **Logs complets** : Toutes les tentatives enregistrées dans `logs_pointages`
    - **Structure conforme** : 6 tables exactes selon spécifications

    ## 🗄️ Base de Données

    L'API interagit avec une base de données MySQL contenant exactement **6 tables** :
    - `users` - Utilisateurs (admin/employee)
    - `sites` - Sites avec coordonnées GPS
    - `assignments` - Assignations employés ↔ sites
    - `pointages` - Pointages avec trigger durée automatique
    - `verifications` - Vérifications manuelles
    - `logs_pointages` - Logs de toutes les tentatives

    <aside>Les exemples de code sont affichés dans la zone sombre à droite. Vous pouvez changer le langage avec les onglets en haut à droite.</aside>

