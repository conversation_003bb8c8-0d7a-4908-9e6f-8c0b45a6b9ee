<?php

/**
 * Script de vérification de la configuration ClockIn
 * Vérifie que tous les composants sont correctement configurés
 */

require_once __DIR__ . '/../../vendor/autoload.php';

class SetupVerifier
{
    private $checks = [];
    private $errors = [];
    private $warnings = [];

    public function run()
    {
        echo "🔍 VÉRIFICATION DE LA CONFIGURATION CLOCKIN\n";
        echo "==========================================\n\n";

        $this->checkEnvironment();
        $this->checkDatabase();
        $this->checkTables();
        $this->checkModels();
        $this->displayResults();

        return empty($this->errors);
    }

    private function checkEnvironment()
    {
        echo "📋 Vérification de l'environnement...\n";

        // Vérifier PHP
        $phpVersion = PHP_VERSION;
        if (version_compare($phpVersion, '8.2.0', '>=')) {
            $this->addCheck("PHP Version ($phpVersion)", true);
        } else {
            $this->addError("PHP Version ($phpVersion) - Minimum requis: 8.2.0");
        }

        // Vérifier les extensions PHP
        $extensions = ['pdo', 'pdo_mysql', 'mbstring', 'openssl', 'json'];
        foreach ($extensions as $ext) {
            if (extension_loaded($ext)) {
                $this->addCheck("Extension PHP: $ext", true);
            } else {
                $this->addError("Extension PHP manquante: $ext");
            }
        }

        // Vérifier le fichier .env
        $envFile = __DIR__ . '/../../.env';
        if (file_exists($envFile)) {
            $this->addCheck("Fichier .env", true);
            $this->checkEnvVariables();
        } else {
            $this->addError("Fichier .env manquant");
        }

        echo "\n";
    }

    private function checkEnvVariables()
    {
        $envFile = __DIR__ . '/../../.env';
        $envContent = file_get_contents($envFile);

        $requiredVars = [
            'DB_CONNECTION' => 'mysql',
            'DB_HOST' => '127.0.0.1',
            'DB_PORT' => '3306',
            'DB_DATABASE' => 'clockin',
            'DB_USERNAME' => 'root'
        ];

        foreach ($requiredVars as $var => $expectedValue) {
            if (preg_match("/^$var=(.*)$/m", $envContent, $matches)) {
                $value = trim($matches[1]);
                if ($value === $expectedValue || ($var === 'DB_USERNAME' && $value === 'root')) {
                    $this->addCheck("Variable ENV: $var", true);
                } else {
                    $this->addWarning("Variable ENV: $var = '$value' (attendu: '$expectedValue')");
                }
            } else {
                $this->addError("Variable ENV manquante: $var");
            }
        }
    }

    private function checkDatabase()
    {
        echo "🗄️ Vérification de la base de données...\n";

        try {
            // Charger la configuration Laravel
            $this->loadLaravelConfig();

            $config = [
                'host' => $_ENV['DB_HOST'] ?? '127.0.0.1',
                'port' => $_ENV['DB_PORT'] ?? '3306',
                'database' => $_ENV['DB_DATABASE'] ?? 'clockin',
                'username' => $_ENV['DB_USERNAME'] ?? 'root',
                'password' => $_ENV['DB_PASSWORD'] ?? '',
                'charset' => $_ENV['DB_CHARSET'] ?? 'utf8mb4'
            ];

            // Test de connexion MySQL
            $pdo = new PDO(
                "mysql:host={$config['host']};port={$config['port']};charset={$config['charset']}",
                $config['username'],
                $config['password'],
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            $this->addCheck("Connexion MySQL", true);

            // Vérifier la base de données
            $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
            $stmt->execute([$config['database']]);
            if ($stmt->fetch()) {
                $this->addCheck("Base de données '{$config['database']}'", true);
            } else {
                $this->addError("Base de données '{$config['database']}' n'existe pas");
            }

            // Test de connexion à la base
            $pdo = new PDO(
                "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
                $config['username'],
                $config['password'],
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );

            $result = $pdo->query("SELECT VERSION() as version")->fetch();
            $this->addCheck("Connexion base de données (MySQL {$result['version']})", true);

        } catch (PDOException $e) {
            $this->addError("Erreur base de données: " . $e->getMessage());
        }

        echo "\n";
    }

    private function checkTables()
    {
        echo "📊 Vérification des tables...\n";

        try {
            $config = [
                'host' => $_ENV['DB_HOST'] ?? '127.0.0.1',
                'port' => $_ENV['DB_PORT'] ?? '3306',
                'database' => $_ENV['DB_DATABASE'] ?? 'clockin',
                'username' => $_ENV['DB_USERNAME'] ?? 'root',
                'password' => $_ENV['DB_PASSWORD'] ?? '',
                'charset' => $_ENV['DB_CHARSET'] ?? 'utf8mb4'
            ];

            $pdo = new PDO(
                "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
                $config['username'],
                $config['password'],
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );

            $requiredTables = [
                'users', 'sites', 'assignments', 'pointages', 
                'verifications', 'logs_pointages', 'migrations'
            ];

            $stmt = $pdo->query("SHOW TABLES");
            $existingTables = $stmt->fetchAll(PDO::FETCH_COLUMN);

            foreach ($requiredTables as $table) {
                if (in_array($table, $existingTables)) {
                    $this->addCheck("Table: $table", true);
                } else {
                    $this->addError("Table manquante: $table");
                }
            }

        } catch (PDOException $e) {
            $this->addError("Erreur vérification tables: " . $e->getMessage());
        }

        echo "\n";
    }

    private function checkModels()
    {
        echo "🏗️ Vérification des modèles...\n";

        $models = [
            'App\\Models\\User',
            'App\\Models\\Site',
            'App\\Models\\Assignment',
            'App\\Models\\Pointage',
            'App\\Models\\Verification',
            'App\\Models\\PointageLog'
        ];

        foreach ($models as $model) {
            if (class_exists($model)) {
                $this->addCheck("Modèle: " . basename($model), true);
            } else {
                $this->addError("Modèle manquant: " . basename($model));
            }
        }

        echo "\n";
    }

    private function loadLaravelConfig()
    {
        $envFile = __DIR__ . '/../../.env';
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
                    list($key, $value) = explode('=', $line, 2);
                    $_ENV[trim($key)] = trim($value);
                }
            }
        }
    }

    private function addCheck($item, $status)
    {
        $this->checks[] = ['item' => $item, 'status' => $status];
        echo ($status ? "✅" : "❌") . " $item\n";
    }

    private function addError($message)
    {
        $this->errors[] = $message;
        echo "❌ $message\n";
    }

    private function addWarning($message)
    {
        $this->warnings[] = $message;
        echo "⚠️ $message\n";
    }

    private function displayResults()
    {
        echo "📊 RÉSULTATS DE LA VÉRIFICATION\n";
        echo "===============================\n\n";

        $totalChecks = count($this->checks);
        $passedChecks = count(array_filter($this->checks, fn($check) => $check['status']));

        echo "✅ Tests réussis: $passedChecks/$totalChecks\n";
        
        if (!empty($this->warnings)) {
            echo "⚠️ Avertissements: " . count($this->warnings) . "\n";
        }
        
        if (!empty($this->errors)) {
            echo "❌ Erreurs: " . count($this->errors) . "\n";
        }

        echo "\n";

        if (empty($this->errors)) {
            echo "🎉 CONFIGURATION VALIDÉE AVEC SUCCÈS!\n";
            echo "Votre projet ClockIn est prêt à fonctionner.\n\n";
            echo "Prochaines étapes:\n";
            echo "1. Configurez JWT: php artisan jwt:secret\n";
            echo "2. Créez un utilisateur admin\n";
            echo "3. Testez les endpoints API\n";
        } else {
            echo "🚨 ERREURS DÉTECTÉES\n";
            echo "Veuillez corriger les erreurs suivantes:\n\n";
            foreach ($this->errors as $error) {
                echo "- $error\n";
            }
        }

        if (!empty($this->warnings)) {
            echo "\n⚠️ AVERTISSEMENTS\n";
            foreach ($this->warnings as $warning) {
                echo "- $warning\n";
            }
        }
    }
}

// Exécution du script
if (php_sapi_name() === 'cli') {
    $verifier = new SetupVerifier();
    $success = $verifier->run();
    exit($success ? 0 : 1);
}
