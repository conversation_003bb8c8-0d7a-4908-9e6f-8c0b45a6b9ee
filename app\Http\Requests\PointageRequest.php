<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class PointageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id_site' => 'required|exists:sites,id_site',
            'debut_latitude' => 'required|numeric|between:-90,90',
            'debut_longitude' => 'required|numeric|between:-180,180',
            'fin_latitude' => 'sometimes|numeric|between:-90,90',
            'fin_longitude' => 'sometimes|numeric|between:-180,180',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'id_site.required' => __('validation.required', ['attribute' => __('pointage.site')]),
            'id_site.exists' => __('validation.exists', ['attribute' => __('pointage.site')]),
            'debut_latitude.required' => __('validation.required', ['attribute' => __('pointage.start_latitude')]),
            'debut_latitude.numeric' => __('validation.numeric', ['attribute' => __('pointage.start_latitude')]),
            'debut_latitude.between' => __('validation.between.numeric', ['attribute' => __('pointage.start_latitude'), 'min' => -90, 'max' => 90]),
            'debut_longitude.required' => __('validation.required', ['attribute' => __('pointage.start_longitude')]),
            'debut_longitude.numeric' => __('validation.numeric', ['attribute' => __('pointage.start_longitude')]),
            'debut_longitude.between' => __('validation.between.numeric', ['attribute' => __('pointage.start_longitude'), 'min' => -180, 'max' => 180]),
            'fin_latitude.numeric' => __('validation.numeric', ['attribute' => __('pointage.end_latitude')]),
            'fin_latitude.between' => __('validation.between.numeric', ['attribute' => __('pointage.end_latitude'), 'min' => -90, 'max' => 90]),
            'fin_longitude.numeric' => __('validation.numeric', ['attribute' => __('pointage.end_longitude')]),
            'fin_longitude.between' => __('validation.between.numeric', ['attribute' => __('pointage.end_longitude'), 'min' => -180, 'max' => 180]),
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'success' => false,
                'message' => __('validation.failed'),
                'errors' => $validator->errors()
            ], 422)
        );
    }
}
