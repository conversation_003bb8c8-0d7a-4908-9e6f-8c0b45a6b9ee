<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer un administrateur par défaut
        \App\Models\User::create([
            'nom' => 'Administrateur Principal',
            'email' => '<EMAIL>',
            'mot_de_passe' => \Illuminate\Support\Facades\Hash::make('admin123456'),
            'role' => 'admin',
            'date_embauche' => now(),
            'numero_cnss' => 'ADM001',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Créer quelques employés de test
        $employees = [
            [
                'nom' => '<PERSON>',
                'email' => '<EMAIL>',
                'mot_de_passe' => \Illuminate\Support\Facades\Hash::make('employee123'),
                'role' => 'employee',
                'date_embauche' => now()->subMonths(6),
                'numero_cnss' => 'EMP001',
            ],
            [
                'nom' => 'Fatima Zahra',
                'email' => '<EMAIL>',
                'mot_de_passe' => \Illuminate\Support\Facades\Hash::make('employee123'),
                'role' => 'employee',
                'date_embauche' => now()->subMonths(3),
                'numero_cnss' => 'EMP002',
            ],
            [
                'nom' => 'Mohamed Alami',
                'email' => '<EMAIL>',
                'mot_de_passe' => \Illuminate\Support\Facades\Hash::make('employee123'),
                'role' => 'employee',
                'date_embauche' => now()->subMonths(1),
                'numero_cnss' => 'EMP003',
            ],
        ];

        foreach ($employees as $employee) {
            \App\Models\User::create(array_merge($employee, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
}
