<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class LocationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id_site' => 'required|exists:sites,id_site',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'id_site.required' => __('validation.required', ['attribute' => __('location.site')]),
            'id_site.exists' => __('validation.exists', ['attribute' => __('location.site')]),
            'latitude.required' => __('validation.required', ['attribute' => __('location.latitude')]),
            'latitude.numeric' => __('validation.numeric', ['attribute' => __('location.latitude')]),
            'latitude.between' => __('validation.between.numeric', ['attribute' => __('location.latitude'), 'min' => -90, 'max' => 90]),
            'longitude.required' => __('validation.required', ['attribute' => __('location.longitude')]),
            'longitude.numeric' => __('validation.numeric', ['attribute' => __('location.longitude')]),
            'longitude.between' => __('validation.between.numeric', ['attribute' => __('location.longitude'), 'min' => -180, 'max' => 180]),
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'success' => false,
                'message' => __('validation.failed'),
                'errors' => $validator->errors()
            ], 422)
        );
    }
}
