<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assignments', function (Blueprint $table) {
            $table->id('id_assignment');
            $table->unsignedBigInteger('id_user');
            $table->unsignedBigInteger('id_site');
            $table->date('date_assignation');
            $table->timestamp('created_at')->useCurrent();

            // Clés étrangères avec CASCADE
            $table->foreign('id_user')->references('id_user')->on('users')->onDelete('cascade');
            $table->foreign('id_site')->references('id_site')->on('sites')->onDelete('cascade');

            // Index pour optimiser les performances
            $table->index('id_user');
            $table->index('id_site');
            $table->index(['id_user', 'id_site']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assignments');
    }
};
