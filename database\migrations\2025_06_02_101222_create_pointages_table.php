<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pointages', function (Blueprint $table) {
            $table->id('id_pointage');
            $table->unsignedBigInteger('id_user');
            $table->unsignedBigInteger('id_site');
            $table->dateTime('debut_pointage');
            $table->dateTime('fin_pointage')->nullable();
            $table->time('duree')->nullable();
            $table->decimal('debut_latitude', 10, 8);
            $table->decimal('debut_longitude', 11, 8);
            $table->decimal('fin_latitude', 10, 8)->nullable();
            $table->decimal('fin_longitude', 11, 8)->nullable();
            $table->timestamps();

            // Clés étrangères avec CASCADE
            $table->foreign('id_user')->references('id_user')->on('users')->onDelete('cascade');
            $table->foreign('id_site')->references('id_site')->on('sites')->onDelete('cascade');

            // Index pour optimiser les performances
            $table->index('id_user');
            $table->index('id_site');
            $table->index(['id_user', 'id_site']);
            $table->index('debut_pointage');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pointages');
    }
};
