name: 'Géolocalisation (Rayon 50m)'
description: |-

  Endpoints pour la vérification de géolocalisation avec validation du rayon de 50m.
  Utilise le calcul Haversine pour une précision GPS optimale.
  Toutes les tentatives sont enregistrées dans la table logs_pointages.
endpoints:
  -
    httpMethods:
      - POST
    uri: api/location/check-location
    metadata:
      groupName: 'Géolocalisation (Rayon 50m)'
      groupDescription: |-

        Endpoints pour la vérification de géolocalisation avec validation du rayon de 50m.
        Utilise le calcul Haversine pour une précision GPS optimale.
        Toutes les tentatives sont enregistrées dans la table logs_pointages.
      subgroup: ''
      subgroupDescription: ''
      title: 'Vérification de localisation (Rayon 50m)'
      description: |-
        Vérifie si l'employé se trouve dans le rayon autorisé de 50m du site assigné.
        Utilise le calcul Haversine pour une précision GPS optimale.
        Enregistre automatiquement un log dans la table logs_pointages.
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      id_site:
        name: id_site
        description: 'ID du site à vérifier.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      latitude:
        name: latitude
        description: "Latitude GPS de l'employé."
        required: true
        example: 33.5731
        type: number
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      longitude:
        name: longitude
        description: "Longitude GPS de l'employé."
        required: true
        example: -7.5898
        type: number
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      id_site: 1
      latitude: 33.5731
      longitude: -7.5898
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "in_range": true,
            "distance": 0,
            "max_distance": 50,
            "message": "location.in_range",
            "site": {
              "id_site": 1,
              "nom_site": "Chantier Centre-Ville Casablanca",
              "latitude": "33.57310000",
              "longitude": "-7.58980000"
            }
          }
        headers: []
        description: 'Dans le rayon (50m)'
        custom: []
      -
        status: 200
        content: |-
          {
            "success": true,
            "in_range": false,
            "distance": 3136.82,
            "max_distance": 50,
            "message": "location.out_of_range"
          }
        headers: []
        description: 'Hors du rayon (>50m)'
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/location/request-verification
    metadata:
      groupName: 'Géolocalisation (Rayon 50m)'
      groupDescription: |-

        Endpoints pour la vérification de géolocalisation avec validation du rayon de 50m.
        Utilise le calcul Haversine pour une précision GPS optimale.
        Toutes les tentatives sont enregistrées dans la table logs_pointages.
      subgroup: ''
      subgroupDescription: ''
      title: 'Demander une vérification de localisation (pour les cas spéciaux)'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      id_site:
        name: id_site
        description: 'The <code>id_site</code> of an existing record in the sites table.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      latitude:
        name: latitude
        description: validation.between.
        required: true
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      longitude:
        name: longitude
        description: validation.between.
        required: true
        example: -180
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      id_site: architecto
      latitude: -89
      longitude: -180
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/location/verifications
    metadata:
      groupName: 'Géolocalisation (Rayon 50m)'
      groupDescription: |-

        Endpoints pour la vérification de géolocalisation avec validation du rayon de 50m.
        Utilise le calcul Haversine pour une précision GPS optimale.
        Toutes les tentatives sont enregistrées dans la table logs_pointages.
      subgroup: ''
      subgroupDescription: ''
      title: "Obtenir l'historique des vérifications"
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/admin/verify-location/{id}'
    metadata:
      groupName: 'Géolocalisation (Rayon 50m)'
      groupDescription: |-

        Endpoints pour la vérification de géolocalisation avec validation du rayon de 50m.
        Utilise le calcul Haversine pour une précision GPS optimale.
        Toutes les tentatives sont enregistrées dans la table logs_pointages.
      subgroup: ''
      subgroupDescription: ''
      title: 'Vérifier une localisation (pour les administrateurs)'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the verify location.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      id_site:
        name: id_site
        description: 'The <code>id_site</code> of an existing record in the sites table.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      latitude:
        name: latitude
        description: validation.between.
        required: true
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      longitude:
        name: longitude
        description: validation.between.
        required: true
        example: -180
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      id_site: architecto
      latitude: -89
      longitude: -180
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/request-verification
    metadata:
      groupName: 'Géolocalisation (Rayon 50m)'
      groupDescription: |-

        Endpoints pour la vérification de géolocalisation avec validation du rayon de 50m.
        Utilise le calcul Haversine pour une précision GPS optimale.
        Toutes les tentatives sont enregistrées dans la table logs_pointages.
      subgroup: ''
      subgroupDescription: ''
      title: 'Demander une vérification de localisation (pour les cas spéciaux)'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      id_site:
        name: id_site
        description: 'The <code>id_site</code> of an existing record in the sites table.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      latitude:
        name: latitude
        description: validation.between.
        required: true
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      longitude:
        name: longitude
        description: validation.between.
        required: true
        example: -180
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      id_site: architecto
      latitude: -89
      longitude: -180
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
