name: 'Pointages (Trigger Durée)'
description: |-

  Endpoints pour la gestion des pointages avec calcul automatique de durée.
  Le trigger MySQL calcule automatiquement la durée via TIMEDIFF(fin_pointage, debut_pointage).
  Validation du rayon de 50m obligatoire pour démarrer un pointage.
endpoints:
  -
    httpMethods:
      - GET
    uri: api/pointages
    metadata:
      groupName: 'Pointages (Trigger Durée)'
      groupDescription: |-

        Endpoints pour la gestion des pointages avec calcul automatique de durée.
        Le trigger MySQL calcule automatiquement la durée via TIMEDIFF(fin_pointage, debut_pointage).
        Validation du rayon de 50m obligatoire pour démarrer un pointage.
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of the resource.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/pointages
    metadata:
      groupName: 'Pointages (Trigger Durée)'
      groupDescription: |-

        Endpoints pour la gestion des pointages avec calcul automatique de durée.
        Le trigger MySQL calcule automatiquement la durée via TIMEDIFF(fin_pointage, debut_pointage).
        Validation du rayon de 50m obligatoire pour démarrer un pointage.
      subgroup: ''
      subgroupDescription: ''
      title: 'Démarrer un pointage'
      description: |-
        Démarre un nouveau pointage pour l'employé connecté.
        Vérifie automatiquement que l'employé est dans le rayon de 50m du site.
        La durée sera calculée automatiquement par le trigger MySQL lors de la fin.
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      id_site:
        name: id_site
        description: 'ID du site pour le pointage.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      debut_latitude:
        name: debut_latitude
        description: 'Latitude GPS de début.'
        required: true
        example: 33.5731
        type: number
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      debut_longitude:
        name: debut_longitude
        description: 'Longitude GPS de début.'
        required: true
        example: -7.5898
        type: number
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      fin_latitude:
        name: fin_latitude
        description: validation.between.
        required: false
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      fin_longitude:
        name: fin_longitude
        description: validation.between.
        required: false
        example: -180
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      id_site: 1
      debut_latitude: 33.5731
      debut_longitude: -7.5898
      fin_latitude: -89
      fin_longitude: -180
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "success": true,
            "message": "pointage.started",
            "data": {
              "id_pointage": 1,
              "id_user": 2,
              "id_site": 1,
              "debut_pointage": "2025-06-03T10:56:35.000000Z",
              "debut_latitude": "33.57310000",
              "debut_longitude": "-7.58980000",
              "fin_pointage": null,
              "duree": null
            }
          }
        headers: []
        description: 'Pointage démarré'
        custom: []
      -
        status: 400
        content: |-
          {
            "success": false,
            "message": "pointage.too_far",
            "distance": 3136.82,
            "max_distance": 50
          }
        headers: []
        description: 'Hors du rayon'
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/pointages/{id}'
    metadata:
      groupName: 'Pointages (Trigger Durée)'
      groupDescription: |-

        Endpoints pour la gestion des pointages avec calcul automatique de durée.
        Le trigger MySQL calcule automatiquement la durée via TIMEDIFF(fin_pointage, debut_pointage).
        Validation du rayon de 50m obligatoire pour démarrer un pointage.
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified resource.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the pointage.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/pointages/{id}'
    metadata:
      groupName: 'Pointages (Trigger Durée)'
      groupDescription: |-

        Endpoints pour la gestion des pointages avec calcul automatique de durée.
        Le trigger MySQL calcule automatiquement la durée via TIMEDIFF(fin_pointage, debut_pointage).
        Validation du rayon de 50m obligatoire pour démarrer un pointage.
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified resource in storage.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the pointage.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      fin_latitude:
        name: fin_latitude
        description: validation.between.
        required: false
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      fin_longitude:
        name: fin_longitude
        description: validation.between.
        required: false
        example: -179
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      fin_latitude: -89
      fin_longitude: -179
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/pointages/{id}'
    metadata:
      groupName: 'Pointages (Trigger Durée)'
      groupDescription: |-

        Endpoints pour la gestion des pointages avec calcul automatique de durée.
        Le trigger MySQL calcule automatiquement la durée via TIMEDIFF(fin_pointage, debut_pointage).
        Validation du rayon de 50m obligatoire pour démarrer un pointage.
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified resource from storage.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the pointage.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/pointages/save-pointage
    metadata:
      groupName: 'Pointages (Trigger Durée)'
      groupDescription: |-

        Endpoints pour la gestion des pointages avec calcul automatique de durée.
        Le trigger MySQL calcule automatiquement la durée via TIMEDIFF(fin_pointage, debut_pointage).
        Validation du rayon de 50m obligatoire pour démarrer un pointage.
      subgroup: ''
      subgroupDescription: ''
      title: 'Démarrer un pointage'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      id_site:
        name: id_site
        description: 'The <code>id_site</code> of an existing record in the sites table.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      debut_latitude:
        name: debut_latitude
        description: validation.between.
        required: true
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      debut_longitude:
        name: debut_longitude
        description: validation.between.
        required: true
        example: -180
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      fin_latitude:
        name: fin_latitude
        description: validation.between.
        required: false
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      fin_longitude:
        name: fin_longitude
        description: validation.between.
        required: false
        example: -180
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      id_site: architecto
      debut_latitude: -89
      debut_longitude: -180
      fin_latitude: -89
      fin_longitude: -180
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/pointages/start
    metadata:
      groupName: 'Pointages (Trigger Durée)'
      groupDescription: |-

        Endpoints pour la gestion des pointages avec calcul automatique de durée.
        Le trigger MySQL calcule automatiquement la durée via TIMEDIFF(fin_pointage, debut_pointage).
        Validation du rayon de 50m obligatoire pour démarrer un pointage.
      subgroup: ''
      subgroupDescription: ''
      title: 'Démarrer un pointage'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      id_site:
        name: id_site
        description: 'The <code>id_site</code> of an existing record in the sites table.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      debut_latitude:
        name: debut_latitude
        description: validation.between.
        required: true
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      debut_longitude:
        name: debut_longitude
        description: validation.between.
        required: true
        example: -180
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      fin_latitude:
        name: fin_latitude
        description: validation.between.
        required: false
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      fin_longitude:
        name: fin_longitude
        description: validation.between.
        required: false
        example: -180
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      id_site: architecto
      debut_latitude: -89
      debut_longitude: -180
      fin_latitude: -89
      fin_longitude: -180
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/pointages/end
    metadata:
      groupName: 'Pointages (Trigger Durée)'
      groupDescription: |-

        Endpoints pour la gestion des pointages avec calcul automatique de durée.
        Le trigger MySQL calcule automatiquement la durée via TIMEDIFF(fin_pointage, debut_pointage).
        Validation du rayon de 50m obligatoire pour démarrer un pointage.
      subgroup: ''
      subgroupDescription: ''
      title: 'Terminer un pointage'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      fin_latitude:
        name: fin_latitude
        description: validation.between.
        required: true
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      fin_longitude:
        name: fin_longitude
        description: validation.between.
        required: true
        example: -179
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      fin_latitude: -89
      fin_longitude: -179
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/pointages/current/status
    metadata:
      groupName: 'Pointages (Trigger Durée)'
      groupDescription: |-

        Endpoints pour la gestion des pointages avec calcul automatique de durée.
        Le trigger MySQL calcule automatiquement la durée via TIMEDIFF(fin_pointage, debut_pointage).
        Validation du rayon de 50m obligatoire pour démarrer un pointage.
      subgroup: ''
      subgroupDescription: ''
      title: 'Obtenir le statut actuel du pointage'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/pointages
    metadata:
      groupName: 'Pointages (Trigger Durée)'
      groupDescription: |-

        Endpoints pour la gestion des pointages avec calcul automatique de durée.
        Le trigger MySQL calcule automatiquement la durée via TIMEDIFF(fin_pointage, debut_pointage).
        Validation du rayon de 50m obligatoire pour démarrer un pointage.
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of the resource.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
