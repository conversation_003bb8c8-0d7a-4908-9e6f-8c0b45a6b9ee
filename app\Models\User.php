<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Ty<PERSON>\JWTAuth\Contracts\JWTSubject;
use Illuminate\Database\Eloquent\Relations\HasMany;

class User extends Authenticatable implements JWTSubject
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    protected $primaryKey = 'id_user';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'nom',
        'email',
        'mot_de_passe',
        'role',
        'date_embauche',
        'numero_cnss',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'mot_de_passe',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'date_embauche' => 'date',
            'mot_de_passe' => 'hashed',
        ];
    }

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }

    /**
     * Get the password for authentication.
     */
    public function getAuthPassword()
    {
        return $this->mot_de_passe;
    }

    /**
     * Relations
     */
    public function assignments(): HasMany
    {
        return $this->hasMany(Assignment::class, 'id_user', 'id_user');
    }

    public function pointages(): HasMany
    {
        return $this->hasMany(Pointage::class, 'id_user', 'id_user');
    }

    public function verifications(): HasMany
    {
        return $this->hasMany(Verification::class, 'id_user', 'id_user');
    }

    public function logsPointages(): HasMany
    {
        return $this->hasMany(PointageLog::class, 'id_user', 'id_user');
    }

    /**
     * Scope pour filtrer par rôle
     */
    public function scopeEmployees($query)
    {
        return $query->where('role', 'employee');
    }

    public function scopeAdmins($query)
    {
        return $query->where('role', 'admin');
    }
}
