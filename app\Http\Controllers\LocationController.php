<?php

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\LocationRequest;
use App\Models\Site;
use App\Models\Assignment;
use App\Models\Verification;
use App\Models\PointageLog;

class LocationController extends Controller
{
    /**
     * Vérifier la localisation par rapport au site (rayon de 50m)
     */
    public function checkLocation(LocationRequest $request): JsonResponse
    {
        $user = Auth::guard('api')->user();
        $site = Site::find($request->id_site);

        if (!$site) {
            return response()->json([
                'success' => false,
                'message' => __('site.not_found')
            ], 404);
        }

        // Vérifier si l'utilisateur est assigné à ce site
        $assignment = Assignment::where('id_user', $user->id_user)
                                ->where('id_site', $request->id_site)
                                ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => __('pointage.not_assigned')
            ], 403);
        }

        // Calculer la distance
        $distance = Site::calculateDistance(
            $request->latitude,
            $request->longitude,
            $site->latitude,
            $site->longitude
        );

        $maxDistance = 50; // 50 mètres comme spécifié
        $isInRange = $distance <= $maxDistance;

        // Enregistrer la tentative dans les logs
        PointageLog::create([
            'id_user' => $user->id_user,
            'id_site' => $request->id_site,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'date_tentative' => now(),
            'succes' => $isInRange,
            'raison_echec' => $isInRange ? null : __('pointage.too_far') . " ({$distance}m > {$maxDistance}m)"
        ]);

        return response()->json([
            'success' => true,
            'in_range' => $isInRange,
            'distance' => round($distance, 2),
            'max_distance' => $maxDistance,
            'message' => $isInRange ? __('location.in_range') : __('location.out_of_range'),
            'site' => [
                'id_site' => $site->id_site,
                'nom_site' => $site->nom_site,
                'latitude' => $site->latitude,
                'longitude' => $site->longitude
            ]
        ]);
    }

    /**
     * Demander une vérification de localisation (pour les cas spéciaux)
     */
    public function requestVerification(LocationRequest $request): JsonResponse
    {
        $user = Auth::guard('api')->user();
        $site = Site::find($request->id_site);

        if (!$site) {
            return response()->json([
                'success' => false,
                'message' => __('site.not_found')
            ], 404);
        }

        // Vérifier si l'utilisateur est assigné à ce site
        $assignment = Assignment::where('id_user', $user->id_user)
                                ->where('id_site', $request->id_site)
                                ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => __('pointage.not_assigned')
            ], 403);
        }

        // Calculer la distance
        $distance = Site::calculateDistance(
            $request->latitude,
            $request->longitude,
            $site->latitude,
            $site->longitude
        );

        // Créer une demande de vérification
        $verification = Verification::create([
            'id_user' => $user->id_user,
            'id_site' => $request->id_site,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'date_verification' => now(),
            'status' => 'pending',
        ]);

        // TODO: Envoyer notification Firebase aux administrateurs

        return response()->json([
            'success' => true,
            'message' => __('verification.requested'),
            'verification' => $verification,
            'distance' => round($distance, 2)
        ], 201);
    }

    /**
     * Vérifier une localisation (pour les administrateurs)
     */
    public function verifyLocation(LocationRequest $request): JsonResponse
    {
        $user = Auth::guard('api')->user();

        // Seuls les admins peuvent vérifier
        if ($user->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => __('errors.admin_required')
            ], 403);
        }

        $verificationId = $request->route('id');
        $verification = Verification::find($verificationId);

        if (!$verification) {
            return response()->json([
                'success' => false,
                'message' => __('verification.not_found')
            ], 404);
        }

        $action = $request->input('action'); // 'approve' ou 'reject'
        $reason = $request->input('reason', '');

        if (!in_array($action, ['approve', 'reject'])) {
            return response()->json([
                'success' => false,
                'message' => 'Action invalide. Utilisez "approve" ou "reject".'
            ], 422);
        }

        DB::transaction(function () use ($verification, $action, $reason) {
            // Mettre à jour seulement le status selon la structure cible
            $verification->update([
                'status' => $action === 'approve' ? 'verified' : 'failed',
            ]);

            // Enregistrer la raison dans les logs selon la structure cible
            if ($action === 'reject' && $reason) {
                PointageLog::logAttempt(
                    $verification->id_user,
                    $verification->id_site,
                    $verification->latitude,
                    $verification->longitude,
                    false,
                    "Vérification rejetée: $reason"
                );
            }

            // TODO: Envoyer notification Firebase à l'employé
        });

        return response()->json([
            'success' => true,
            'message' => $action === 'approve' ? 
                __('verification.approved') : 
                __('verification.rejected'),
            'verification' => $verification->fresh()
        ]);
    }

    /**
     * Obtenir l'historique des vérifications
     */
    public function getVerifications(): JsonResponse
    {
        $user = Auth::guard('api')->user();
        $query = Verification::with(['user', 'site']);

        // Si l'utilisateur n'est pas admin, ne montrer que ses vérifications
        if ($user->role !== 'admin') {
            $query->where('id_user', $user->id_user);
        }

        $verifications = $query->orderBy('date_verification', 'desc')
                              ->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $verifications
        ]);
    }
}
