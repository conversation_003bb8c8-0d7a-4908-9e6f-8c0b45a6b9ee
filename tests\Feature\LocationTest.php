<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Site;
use App\Models\Assignment;
use App\Models\PointageLog;
use Illuminate\Support\Facades\Hash;

class LocationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $site;
    protected $token;

    protected function setUp(): void
    {
        parent::setUp();

        // Créer un utilisateur employé selon la structure cible
        $this->user = User::create([
            'nom' => 'Test Employee Location',
            'email' => '<EMAIL>',
            'mot_de_passe' => Hash::make('password123'),
            'role' => 'employee',
            'date_embauche' => now()->toDateString(),
            'numero_cnss' => 'LOC001',
        ]);

        // Créer un site selon la structure cible
        $this->site = Site::create([
            'nom_site' => 'Site Test Location',
            'latitude' => 33.5731,
            'longitude' => -7.5898,
        ]);

        // Assigner l'utilisateur au site selon la structure cible
        Assignment::create([
            'id_user' => $this->user->id_user,
            'id_site' => $this->site->id_site,
            'date_assignation' => now()->toDateString(),
        ]);

        // Se connecter et obtenir le token
        $response = $this->postJson('/api/auth/login', [
            'email' => '<EMAIL>',
            'mot_de_passe' => 'password123',
        ]);

        $this->token = $response->json('authorization.token');
    }

    /**
     * Test de vérification de localisation dans le rayon autorisé (50m)
     */
    public function test_check_location_within_50m_radius(): void
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
            'Accept-Language' => 'fr',
        ])->postJson('/api/location/check-location', [
            'id_site' => $this->site->id_site,
            'latitude' => 33.5731, // Même position que le site
            'longitude' => -7.5898,
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'in_range' => true,
                    'max_distance' => 50, // Rayon de 50m selon les spécifications
                ])
                ->assertJsonStructure([
                    'success',
                    'in_range',
                    'distance',
                    'max_distance',
                    'message',
                    'site',
                ]);

        // Vérifier qu'un log de succès a été créé
        $this->assertDatabaseHas('logs_pointages', [
            'id_user' => $this->user->id_user,
            'id_site' => $this->site->id_site,
            'succes' => true,
        ]);
    }

    /**
     * Test de vérification de localisation hors du rayon de 50m
     */
    public function test_check_location_outside_50m_radius(): void
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
            'Accept-Language' => 'fr',
        ])->postJson('/api/location/check-location', [
            'id_site' => $this->site->id_site,
            'latitude' => 33.6000, // Position éloignée (plus de 50m)
            'longitude' => -7.6000,
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'in_range' => false,
                    'max_distance' => 50,
                ]);

        // Vérifier qu'un log d'échec a été créé avec raison
        $this->assertDatabaseHas('logs_pointages', [
            'id_user' => $this->user->id_user,
            'id_site' => $this->site->id_site,
            'succes' => false,
        ]);

        $log = PointageLog::where('id_user', $this->user->id_user)
                         ->where('succes', false)
                         ->latest()
                         ->first();

        $this->assertStringContainsString('50m', $log->raison_echec);
    }

    /**
     * Test de demande de vérification selon la structure cible
     */
    public function test_request_verification_structure_conforme(): void
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
            'Accept-Language' => 'fr',
        ])->postJson('/api/location/request-verification', [
            'id_site' => $this->site->id_site,
            'latitude' => 33.5800, // Position légèrement éloignée
            'longitude' => -7.5900,
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'verification' => [
                        'id_verification',
                        'id_user',
                        'id_site',
                        'latitude',
                        'longitude',
                        'date_verification',
                        'status',
                        'created_at',
                        // Pas de 'resultat' ni 'distance_calculee' selon la structure cible
                    ],
                    'distance',
                    'note',
                ]);

        // Vérifier que la vérification a été créée selon la structure cible
        $this->assertDatabaseHas('verifications', [
            'id_user' => $this->user->id_user,
            'id_site' => $this->site->id_site,
            'status' => 'pending',
        ]);

        // Vérifier que les champs supprimés ne sont pas présents
        $verification = \App\Models\Verification::latest()->first();
        $this->assertArrayNotHasKey('resultat', $verification->toArray());
        $this->assertArrayNotHasKey('distance_calculee', $verification->toArray());
    }

    /**
     * Test d'accès sans assignation au site
     */
    public function test_check_location_not_assigned(): void
    {
        // Créer un autre site non assigné
        $otherSite = Site::create([
            'nom_site' => 'Site Non Assigné',
            'latitude' => 33.6000,
            'longitude' => -7.6000,
        ]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
            'Accept-Language' => 'fr',
        ])->postJson('/api/location/check-location', [
            'id_site' => $otherSite->id_site,
            'latitude' => 33.6000,
            'longitude' => -7.6000,
        ]);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                ]);
    }

    /**
     * Test de validation des données de localisation
     */
    public function test_location_validation(): void
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/location/check-location', [
            'id_site' => 'invalid',
            'latitude' => 'invalid',
            'longitude' => 'invalid',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['id_site', 'latitude', 'longitude']);
    }
}
