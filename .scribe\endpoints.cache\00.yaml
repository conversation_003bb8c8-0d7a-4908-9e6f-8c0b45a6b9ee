## Autogenerated by <PERSON><PERSON><PERSON>. DO NOT MODIFY.

name: Authentification
description: |-

  Endpoints pour l'authentification JWT des utilisateurs.
  Gère la connexion, déconnexion et récupération du profil utilisateur.
endpoints:
  -
    httpMethods:
      - POST
    uri: api/auth/login
    metadata:
      groupName: Authentification
      groupDescription: |-

        Endpoints pour l'authentification JWT des utilisateurs.
        Gère la connexion, déconnexion et récupération du profil utilisateur.
      subgroup: ''
      subgroupDescription: ''
      title: 'Connexion utilisateur'
      description: |-
        Authentifie un utilisateur avec email et mot de passe, retourne un token JWT.
        Le token expire après 1 heure et doit être inclus dans l'header Authorization.
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      email:
        name: email
        description: "Email de l'utilisateur."
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      mot_de_passe:
        name: mot_de_passe
        description: "Mot de passe de l'utilisateur."
        required: true
        example: admin123456
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      email: <EMAIL>
      mot_de_passe: admin123456
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "user": {
              "id_user": 1,
              "nom": "Administrateur Principal",
              "email": "<EMAIL>",
              "role": "admin",
              "date_embauche": "2025-01-01T00:00:00.000000Z",
              "numero_cnss": "ADM001"
            },
            "authorization": {
              "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
              "type": "bearer",
              "expires_in": 3600
            }
          }
        headers: []
        description: 'Connexion réussie'
        custom: []
      -
        status: 401
        content: |-
          {
            "success": false,
            "message": "auth.failed"
          }
        headers: []
        description: 'Identifiants incorrects'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/auth/register
    metadata:
      groupName: Authentification
      groupDescription: |-

        Endpoints pour l'authentification JWT des utilisateurs.
        Gère la connexion, déconnexion et récupération du profil utilisateur.
      subgroup: ''
      subgroupDescription: ''
      title: 'Register a User.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      nom:
        name: nom
        description: validation.between.
        required: true
        example: bng
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      email:
        name: email
        description: 'validation.email validation.max.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      mot_de_passe:
        name: mot_de_passe
        description: validation.min.
        required: true
        example: dljnikhwaykcmyuwpwlvqwrsitcpscqldzsnrwtujwvlxj
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      role:
        name: role
        description: ''
        required: false
        example: employee
        type: string
        enumValues:
          - admin
          - employee
        exampleWasSpecified: false
        nullable: false
        custom: []
      date_embauche:
        name: date_embauche
        description: validation.date.
        required: false
        example: '2025-06-03T14:59:57'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      numero_cnss:
        name: numero_cnss
        description: validation.max.
        required: false
        example: k
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      nom: bng
      email: <EMAIL>
      mot_de_passe: dljnikhwaykcmyuwpwlvqwrsitcpscqldzsnrwtujwvlxj
      role: employee
      date_embauche: '2025-06-03T14:59:57'
      numero_cnss: k
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/auth/logout
    metadata:
      groupName: Authentification
      groupDescription: |-

        Endpoints pour l'authentification JWT des utilisateurs.
        Gère la connexion, déconnexion et récupération du profil utilisateur.
      subgroup: ''
      subgroupDescription: ''
      title: 'Déconnexion utilisateur'
      description: |-
        Invalide le token JWT de l'utilisateur connecté.
        Le token ne pourra plus être utilisé après cette opération.
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "auth.logout_success"
          }
        headers: []
        description: 'Déconnexion réussie'
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/auth/refresh
    metadata:
      groupName: Authentification
      groupDescription: |-

        Endpoints pour l'authentification JWT des utilisateurs.
        Gère la connexion, déconnexion et récupération du profil utilisateur.
      subgroup: ''
      subgroupDescription: ''
      title: 'Refresh a token.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/auth/me
    metadata:
      groupName: Authentification
      groupDescription: |-

        Endpoints pour l'authentification JWT des utilisateurs.
        Gère la connexion, déconnexion et récupération du profil utilisateur.
      subgroup: ''
      subgroupDescription: ''
      title: 'Profil utilisateur connecté'
      description: |-
        Récupère les informations de l'utilisateur actuellement connecté.
        Nécessite un token JWT valide dans l'header Authorization.
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer Bearer {YOUR_JWT_TOKEN}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "user": {
              "id_user": 2,
              "nom": "samah",
              "email": "<EMAIL>",
              "role": "employee",
              "date_embauche": "2024-06-01T00:00:00.000000Z",
              "numero_cnss": "EMP001"
            }
          }
        headers: []
        description: 'Profil récupéré'
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpL2F1dGgvbG9naW4iLCJpYXQiOjE3NDg5NDc3MzQsImV4cCI6MTc0ODk1MTMzNCwibmJmIjoxNzQ4OTQ3NzM0LCJqdGkiOiJRRzR3a2R0MFdYbnNJQ0UyIiwic3ViIjoiMiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.ns-zSir2ij3kh5xu-cPgRILVlJGNPfnTFhKLRbdHgoo'
    controller: null
    method: null
    route: null
    custom: []
