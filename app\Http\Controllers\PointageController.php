<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\Pointage;
use App\Models\Site;
use App\Models\PointageLog;
use App\Models\Assignment;
use App\Http\Requests\PointageRequest;
use Carbon\Carbon;

class PointageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::guard('api')->user();
        $query = Pointage::with(['user', 'site']);

        // Si l'utilisateur n'est pas admin, ne montrer que ses pointages
        if ($user->role !== 'admin') {
            $query->where('id_user', $user->id_user);
        }

        // Filtres optionnels
        if ($request->has('user_id') && $user->role === 'admin') {
            $query->where('id_user', $request->user_id);
        }

        if ($request->has('site_id')) {
            $query->where('id_site', $request->site_id);
        }

        if ($request->has('date_debut')) {
            $query->whereDate('debut_pointage', '>=', $request->date_debut);
        }

        if ($request->has('date_fin')) {
            $query->whereDate('debut_pointage', '<=', $request->date_fin);
        }

        $pointages = $query->orderBy('debut_pointage', 'desc')
                          ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $pointages
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(PointageRequest $request): JsonResponse
    {
        $user = Auth::guard('api')->user();

        // Vérifier si l'utilisateur est assigné à ce site
        $assignment = Assignment::where('id_user', $user->id_user)
                                ->where('id_site', $request->id_site)
                                ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => __('pointage.not_assigned')
            ], 403);
        }

        // Vérifier si l'utilisateur a déjà un pointage en cours
        $activePointage = Pointage::where('id_user', $user->id_user)
                                 ->whereNull('fin_pointage')
                                 ->first();

        if ($activePointage) {
            return response()->json([
                'success' => false,
                'message' => __('pointage.already_active')
            ], 400);
        }

        // Vérifier la géolocalisation
        $site = Site::find($request->id_site);
        $distance = $this->calculateDistance(
            $request->debut_latitude,
            $request->debut_longitude,
            $site->latitude,
            $site->longitude
        );

        $maxDistance = 50; // 50 mètres comme spécifié dans les exigences
        if ($distance > $maxDistance) {
            // Enregistrer la tentative échouée
            PointageLog::create([
                'id_user' => $user->id_user,
                'id_site' => $request->id_site,
                'latitude' => $request->debut_latitude,
                'longitude' => $request->debut_longitude,
                'date_tentative' => now(),
                'succes' => false,
                'raison_echec' => __('pointage.too_far') . ": {$distance}m (max: {$maxDistance}m)"
            ]);

            return response()->json([
                'success' => false,
                'message' => __('pointage.too_far'),
                'distance' => round($distance, 2),
                'max_distance' => $maxDistance
            ], 400);
        }

        // Créer le pointage
        $pointage = Pointage::create([
            'id_user' => $user->id_user,
            'id_site' => $request->id_site,
            'debut_pointage' => now(),
            'debut_latitude' => $request->debut_latitude,
            'debut_longitude' => $request->debut_longitude,
        ]);

        // Enregistrer la tentative réussie
        PointageLog::create([
            'id_user' => $user->id_user,
            'id_site' => $request->id_site,
            'latitude' => $request->debut_latitude,
            'longitude' => $request->debut_longitude,
            'date_tentative' => now(),
            'succes' => true,
            'raison_echec' => null
        ]);

        return response()->json([
            'success' => true,
            'message' => __('pointage.started'),
            'data' => $pointage->load(['user', 'site'])
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        $user = Auth::guard('api')->user();
        $query = Pointage::with(['user', 'site']);

        // Si l'utilisateur n'est pas admin, ne montrer que ses pointages
        if ($user->role !== 'admin') {
            $query->where('id_user', $user->id_user);
        }

        $pointage = $query->find($id);

        if (!$pointage) {
            return response()->json([
                'success' => false,
                'message' => 'Pointage non trouvé'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $pointage
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $user = Auth::guard('api')->user();
        $query = Pointage::query();

        // Si l'utilisateur n'est pas admin, ne modifier que ses pointages
        if ($user->role !== 'admin') {
            $query->where('id_user', $user->id_user);
        }

        $pointage = $query->find($id);

        if (!$pointage) {
            return response()->json([
                'success' => false,
                'message' => 'Pointage non trouvé'
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'fin_latitude' => 'sometimes|numeric|between:-90,90',
            'fin_longitude' => 'sometimes|numeric|between:-180,180',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Données de validation invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        // Mettre à jour le pointage
        if ($request->has('fin_latitude') && $request->has('fin_longitude')) {
            $pointage->fin_pointage = now();
            $pointage->fin_latitude = $request->fin_latitude;
            $pointage->fin_longitude = $request->fin_longitude;
            // La durée est calculée automatiquement par le trigger MySQL
        }

        $pointage->save();

        return response()->json([
            'success' => true,
            'message' => 'Pointage mis à jour avec succès',
            'data' => $pointage->load(['user', 'site'])
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        $user = Auth::guard('api')->user();

        // Seuls les admins peuvent supprimer des pointages
        if ($user->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Accès refusé'
            ], 403);
        }

        $pointage = Pointage::find($id);

        if (!$pointage) {
            return response()->json([
                'success' => false,
                'message' => 'Pointage non trouvé'
            ], 404);
        }

        $pointage->delete();

        return response()->json([
            'success' => true,
            'message' => 'Pointage supprimé avec succès'
        ]);
    }

    /**
     * Démarrer un pointage
     */
    public function startPointage(PointageRequest $request): JsonResponse
    {
        return $this->store($request);
    }

    /**
     * Terminer un pointage
     */
    public function endPointage(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'fin_latitude' => 'required|numeric|between:-90,90',
            'fin_longitude' => 'required|numeric|between:-180,180',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Données de validation invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::guard('api')->user();

        // Trouver le pointage actif
        $pointage = Pointage::where('id_user', $user->id_user)
                           ->whereNull('fin_pointage')
                           ->first();

        if (!$pointage) {
            return response()->json([
                'success' => false,
                'message' => 'Aucun pointage actif trouvé'
            ], 404);
        }

        // Terminer le pointage
        $pointage->fin_pointage = now();
        $pointage->fin_latitude = $request->fin_latitude;
        $pointage->fin_longitude = $request->fin_longitude;
        // La durée est calculée automatiquement par le trigger MySQL
        $pointage->save();

        return response()->json([
            'success' => true,
            'message' => 'Pointage terminé avec succès',
            'data' => $pointage->load(['user', 'site'])
        ]);
    }

    /**
     * Obtenir le statut actuel du pointage
     */
    public function getCurrentStatus(): JsonResponse
    {
        $user = Auth::guard('api')->user();

        $activePointage = Pointage::where('id_user', $user->id_user)
                                 ->whereNull('fin_pointage')
                                 ->with(['site'])
                                 ->first();

        return response()->json([
            'success' => true,
            'has_active_pointage' => !is_null($activePointage),
            'active_pointage' => $activePointage
        ]);
    }

    /**
     * Calculer la distance entre deux points géographiques
     */
    private function calculateDistance($lat1, $lon1, $lat2, $lon2): float
    {
        $earthRadius = 6371000; // Rayon de la Terre en mètres

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat/2) * sin($dLat/2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLon/2) * sin($dLon/2);

        $c = 2 * atan2(sqrt($a), sqrt(1-$a));

        return $earthRadius * $c;
    }
}
