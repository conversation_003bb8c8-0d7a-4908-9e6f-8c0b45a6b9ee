-- =====================================================
-- SCRIPT DE CORRECTION DE LA BASE DE DONNÉES CLOCKIN
-- Objectif: <PERSON><PERSON>r la structure avec les spécifications exactes
-- =====================================================

-- Désactiver les vérifications de clés étrangères temporairement
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. SUPPRESSION DES TABLES NON CONFORMES
-- =====================================================

-- Supprimer la table migrations comme demandé
DROP TABLE IF EXISTS `migrations`;

-- Supprimer les tables dupliquées/non conformes
DROP TABLE IF EXISTS `assignments_table_clockin`;
DROP TABLE IF EXISTS `pointages_table_clockin`;
DROP TABLE IF EXISTS `sites_table_clockin`;
DROP TABLE IF EXISTS `users_table_clockin`;

-- Supprimer les tables Laravel non nécessaires
DROP TABLE IF EXISTS `cache`;
DROP TABLE IF EXISTS `cache_locks`;
DROP TABLE IF EXISTS `jobs`;
DROP TABLE IF EXISTS `job_batches`;
DROP TABLE IF EXISTS `failed_jobs`;
DROP TABLE IF EXISTS `sessions`;
DROP TABLE IF EXISTS `password_reset_tokens`;

-- =====================================================
-- 2. RECRÉATION DES TABLES SELON LA STRUCTURE CIBLE
-- =====================================================

-- Table users (structure cible exacte)
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id_user` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `nom` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL UNIQUE,
  `mot_de_passe` varchar(255) NOT NULL,
  `role` enum('admin','employee') NOT NULL DEFAULT 'employee',
  `date_embauche` date NULL,
  `numero_cnss` varchar(255) NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id_user`),
  UNIQUE KEY `users_email_unique` (`email`),
  KEY `idx_users_email` (`email`),
  KEY `idx_users_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table sites (structure cible exacte)
DROP TABLE IF EXISTS `sites`;
CREATE TABLE `sites` (
  `id_site` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `nom_site` varchar(255) NOT NULL,
  `latitude` decimal(10,8) NOT NULL,
  `longitude` decimal(11,8) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id_site`),
  KEY `idx_sites_location` (`latitude`, `longitude`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table assignments (structure cible exacte)
DROP TABLE IF EXISTS `assignments`;
CREATE TABLE `assignments` (
  `id_assignment` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `id_user` bigint(20) UNSIGNED NOT NULL,
  `id_site` bigint(20) UNSIGNED NOT NULL,
  `date_assignation` date NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id_assignment`),
  KEY `idx_assignments_user` (`id_user`),
  KEY `idx_assignments_site` (`id_site`),
  KEY `idx_assignments_date` (`date_assignation`),
  CONSTRAINT `assignments_id_user_foreign` FOREIGN KEY (`id_user`) REFERENCES `users` (`id_user`) ON DELETE CASCADE,
  CONSTRAINT `assignments_id_site_foreign` FOREIGN KEY (`id_site`) REFERENCES `sites` (`id_site`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table pointages (structure cible exacte)
DROP TABLE IF EXISTS `pointages`;
CREATE TABLE `pointages` (
  `id_pointage` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `id_user` bigint(20) UNSIGNED NOT NULL,
  `id_site` bigint(20) UNSIGNED NOT NULL,
  `debut_pointage` datetime NOT NULL,
  `fin_pointage` datetime NULL,
  `duree` time NULL,
  `debut_latitude` decimal(10,8) NOT NULL,
  `debut_longitude` decimal(11,8) NOT NULL,
  `fin_latitude` decimal(10,8) NULL,
  `fin_longitude` decimal(11,8) NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id_pointage`),
  KEY `idx_pointages_user` (`id_user`),
  KEY `idx_pointages_site` (`id_site`),
  KEY `idx_pointages_date` (`debut_pointage`),
  KEY `idx_pointages_status` (`fin_pointage`),
  CONSTRAINT `pointages_id_user_foreign` FOREIGN KEY (`id_user`) REFERENCES `users` (`id_user`) ON DELETE CASCADE,
  CONSTRAINT `pointages_id_site_foreign` FOREIGN KEY (`id_site`) REFERENCES `sites` (`id_site`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table verifications (structure cible exacte)
DROP TABLE IF EXISTS `verifications`;
CREATE TABLE `verifications` (
  `id_verification` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `id_user` bigint(20) UNSIGNED NOT NULL,
  `id_site` bigint(20) UNSIGNED NOT NULL,
  `latitude` decimal(10,8) NOT NULL,
  `longitude` decimal(11,8) NOT NULL,
  `date_verification` datetime NOT NULL,
  `status` enum('pending','verified','failed') NOT NULL DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id_verification`),
  KEY `idx_verifications_user` (`id_user`),
  KEY `idx_verifications_site` (`id_site`),
  KEY `idx_verifications_status` (`status`),
  KEY `idx_verifications_date` (`date_verification`),
  CONSTRAINT `verifications_id_user_foreign` FOREIGN KEY (`id_user`) REFERENCES `users` (`id_user`) ON DELETE CASCADE,
  CONSTRAINT `verifications_id_site_foreign` FOREIGN KEY (`id_site`) REFERENCES `sites` (`id_site`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table logs_pointages (structure cible exacte)
DROP TABLE IF EXISTS `logs_pointages`;
CREATE TABLE `logs_pointages` (
  `id_log` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `id_user` bigint(20) UNSIGNED NOT NULL,
  `id_site` bigint(20) UNSIGNED NOT NULL,
  `latitude` decimal(10,8) NULL,
  `longitude` decimal(11,8) NULL,
  `date_tentative` datetime NOT NULL,
  `succes` tinyint(1) NOT NULL,
  `raison_echec` text NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id_log`),
  KEY `idx_logs_user` (`id_user`),
  KEY `idx_logs_site` (`id_site`),
  KEY `idx_logs_date` (`date_tentative`),
  KEY `idx_logs_success` (`succes`),
  CONSTRAINT `logs_pointages_id_user_foreign` FOREIGN KEY (`id_user`) REFERENCES `users` (`id_user`) ON DELETE CASCADE,
  CONSTRAINT `logs_pointages_id_site_foreign` FOREIGN KEY (`id_site`) REFERENCES `sites` (`id_site`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 3. CRÉATION DU TRIGGER POUR CALCUL AUTOMATIQUE DE DURÉE
-- =====================================================

-- Supprimer le trigger s'il existe déjà
DROP TRIGGER IF EXISTS `calculate_duree_pointage`;

-- Créer le trigger pour calculer automatiquement la durée
DELIMITER $$
CREATE TRIGGER `calculate_duree_pointage`
BEFORE UPDATE ON `pointages`
FOR EACH ROW
BEGIN
    IF NEW.fin_pointage IS NOT NULL AND NEW.debut_pointage IS NOT NULL THEN
        SET NEW.duree = TIMEDIFF(NEW.fin_pointage, NEW.debut_pointage);
    END IF;
END$$
DELIMITER ;

-- Réactiver les vérifications de clés étrangères
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 4. INSERTION DES DONNÉES DE TEST
-- =====================================================

-- Insérer un administrateur par défaut
INSERT INTO `users` (`nom`, `email`, `mot_de_passe`, `role`, `date_embauche`, `numero_cnss`, `created_at`, `updated_at`) VALUES
('Administrateur Principal', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', '2025-01-01', 'ADM001', NOW(), NOW());

-- Insérer quelques employés de test
INSERT INTO `users` (`nom`, `email`, `mot_de_passe`, `role`, `date_embauche`, `numero_cnss`, `created_at`, `updated_at`) VALUES
('Ahmed Benali', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'employee', '2024-06-01', 'EMP001', NOW(), NOW()),
('Fatima Zahra', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'employee', '2024-09-01', 'EMP002', NOW(), NOW()),
('Mohamed Alami', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'employee', '2024-11-01', 'EMP003', NOW(), NOW());

-- Insérer des sites de test
INSERT INTO `sites` (`nom_site`, `latitude`, `longitude`, `created_at`, `updated_at`) VALUES
('Chantier Centre-Ville Casablanca', 33.57310000, -7.58980000, NOW(), NOW()),
('Projet Résidentiel Nord', 33.58920000, -7.60360000, NOW(), NOW()),
('Construction Industrielle Est', 33.56500000, -7.57000000, NOW(), NOW()),
('Rénovation Quartier Historique', 33.57200000, -7.59500000, NOW(), NOW());

-- Insérer quelques assignations de test
INSERT INTO `assignments` (`id_user`, `id_site`, `date_assignation`, `created_at`) VALUES
(2, 1, '2025-01-01', NOW()),
(3, 1, '2025-01-01', NOW()),
(4, 2, '2025-01-01', NOW()),
(2, 3, '2025-01-02', NOW());

-- =====================================================
-- SCRIPT TERMINÉ AVEC SUCCÈS
-- =====================================================
