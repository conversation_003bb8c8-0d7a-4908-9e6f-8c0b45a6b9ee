<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ajouter les champs manquants à la table verifications
        Schema::table('verifications', function (Blueprint $table) {
            if (!Schema::hasColumn('verifications', 'resultat')) {
                $table->string('resultat')->nullable()->after('status');
            }
            if (!Schema::hasColumn('verifications', 'distance_calculee')) {
                $table->decimal('distance_calculee', 8, 2)->nullable()->after('resultat');
            }
        });

        // Ajouter le champ manquant à la table logs_pointages
        Schema::table('logs_pointages', function (Blueprint $table) {
            if (!Schema::hasColumn('logs_pointages', 'raison_echec')) {
                $table->text('raison_echec')->nullable()->after('succes');
            }
        });

        // Ajouter des index pour optimiser les performances
        Schema::table('pointages', function (Blueprint $table) {
            $table->index(['id_user', 'debut_pointage'], 'idx_pointages_user_date');
            $table->index(['id_site', 'debut_pointage'], 'idx_pointages_site_date');
        });

        Schema::table('logs_pointages', function (Blueprint $table) {
            $table->index(['id_user', 'date_tentative'], 'idx_logs_user_date');
            $table->index('succes', 'idx_logs_success');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('verifications', function (Blueprint $table) {
            $table->dropColumn(['resultat', 'distance_calculee']);
        });

        Schema::table('logs_pointages', function (Blueprint $table) {
            $table->dropColumn('raison_echec');
        });

        Schema::table('pointages', function (Blueprint $table) {
            $table->dropIndex('idx_pointages_user_date');
            $table->dropIndex('idx_pointages_site_date');
        });

        Schema::table('logs_pointages', function (Blueprint $table) {
            $table->dropIndex('idx_logs_user_date');
            $table->dropIndex('idx_logs_success');
        });
    }
};
