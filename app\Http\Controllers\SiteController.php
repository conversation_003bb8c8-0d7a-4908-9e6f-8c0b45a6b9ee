<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\Site;
use App\Models\Assignment;
use App\Models\Pointage;
use App\Models\Verification;

class SiteController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::guard('api')->user();
        $query = Site::query();

        // Si l'utilisateur n'est pas admin, ne montrer que ses sites assignés
        if ($user->role !== 'admin') {
            $assignedSiteIds = Assignment::where('id_user', $user->id_user)
                                        ->pluck('id_site');
            $query->whereIn('id_site', $assignedSiteIds);
        }

        // Inclure les relations si demandé
        if ($request->has('with_assignments')) {
            $query->with('assignments.user');
        }

        if ($request->has('with_pointages')) {
            $query->with('pointages.user');
        }

        $sites = $query->orderBy('nom_site')->get();

        return response()->json([
            'success' => true,
            'data' => $sites
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $user = Auth::guard('api')->user();

        // Seuls les admins peuvent créer des sites
        if ($user->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Accès refusé. Droits administrateur requis.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'nom_site' => 'required|string|max:255|unique:sites',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Données de validation invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        $site = Site::create([
            'nom_site' => $request->nom_site,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Site créé avec succès',
            'data' => $site
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        $user = Auth::guard('api')->user();
        $query = Site::with(['assignments.user', 'pointages.user']);

        // Si l'utilisateur n'est pas admin, vérifier l'accès au site
        if ($user->role !== 'admin') {
            $hasAccess = Assignment::where('id_user', $user->id_user)
                                  ->where('id_site', $id)
                                  ->exists();
            
            if (!$hasAccess) {
                return response()->json([
                    'success' => false,
                    'message' => 'Accès refusé à ce site'
                ], 403);
            }
        }

        $site = $query->find($id);

        if (!$site) {
            return response()->json([
                'success' => false,
                'message' => 'Site non trouvé'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $site
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $user = Auth::guard('api')->user();

        // Seuls les admins peuvent modifier des sites
        if ($user->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Accès refusé. Droits administrateur requis.'
            ], 403);
        }

        $site = Site::find($id);

        if (!$site) {
            return response()->json([
                'success' => false,
                'message' => 'Site non trouvé'
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'nom_site' => 'sometimes|string|max:255|unique:sites,nom_site,' . $id . ',id_site',
            'latitude' => 'sometimes|numeric|between:-90,90',
            'longitude' => 'sometimes|numeric|between:-180,180',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Données de validation invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        $site->update($request->only(['nom_site', 'latitude', 'longitude']));

        return response()->json([
            'success' => true,
            'message' => 'Site mis à jour avec succès',
            'data' => $site
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        $user = Auth::guard('api')->user();

        // Seuls les admins peuvent supprimer des sites
        if ($user->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Accès refusé. Droits administrateur requis.'
            ], 403);
        }

        $site = Site::find($id);

        if (!$site) {
            return response()->json([
                'success' => false,
                'message' => 'Site non trouvé'
            ], 404);
        }

        // Vérifier s'il y a des pointages associés
        $hasPointages = Pointage::where('id_site', $id)->exists();
        if ($hasPointages) {
            return response()->json([
                'success' => false,
                'message' => 'Impossible de supprimer le site. Des pointages y sont associés.'
            ], 400);
        }

        // Supprimer les assignations associées
        Assignment::where('id_site', $id)->delete();

        $site->delete();

        return response()->json([
            'success' => true,
            'message' => 'Site supprimé avec succès'
        ]);
    }

    /**
     * Obtenir les pointages d'un site
     */
    public function pointages(Request $request, string $id): JsonResponse
    {
        $user = Auth::guard('api')->user();

        // Si l'utilisateur n'est pas admin, vérifier l'accès au site
        if ($user->role !== 'admin') {
            $hasAccess = Assignment::where('id_user', $user->id_user)
                                  ->where('id_site', $id)
                                  ->exists();
            
            if (!$hasAccess) {
                return response()->json([
                    'success' => false,
                    'message' => 'Accès refusé à ce site'
                ], 403);
            }
        }

        $site = Site::find($id);

        if (!$site) {
            return response()->json([
                'success' => false,
                'message' => 'Site non trouvé'
            ], 404);
        }

        $query = Pointage::where('id_site', $id)->with('user');

        // Filtres optionnels
        if ($request->has('date_debut')) {
            $query->whereDate('debut_pointage', '>=', $request->date_debut);
        }

        if ($request->has('date_fin')) {
            $query->whereDate('debut_pointage', '<=', $request->date_fin);
        }

        $pointages = $query->orderBy('debut_pointage', 'desc')
                          ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'site' => $site,
            'pointages' => $pointages
        ]);
    }

    /**
     * Vérifier la localisation par rapport au site
     */
    public function verifyLocation(Request $request, string $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Données de validation invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::guard('api')->user();
        $site = Site::find($id);

        if (!$site) {
            return response()->json([
                'success' => false,
                'message' => 'Site non trouvé'
            ], 404);
        }

        // Vérifier si l'utilisateur est assigné à ce site
        $assignment = Assignment::where('id_user', $user->id_user)
                                ->where('id_site', $id)
                                ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'Vous n\'êtes pas assigné à ce site'
            ], 403);
        }

        // Calculer la distance
        $distance = $this->calculateDistance(
            $request->latitude,
            $request->longitude,
            $site->latitude,
            $site->longitude
        );

        $maxDistance = 100; // 100 mètres de tolérance
        $isInRange = $distance <= $maxDistance;

        // Enregistrer la demande de vérification
        Verification::create([
            'id_user' => $user->id_user,
            'id_site' => $id,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'date_verification' => now(),
            'resultat' => $isInRange ? 'valide' : 'invalide',
            'distance_calculee' => $distance,
        ]);

        return response()->json([
            'success' => true,
            'in_range' => $isInRange,
            'distance' => round($distance, 2),
            'max_distance' => $maxDistance,
            'site' => $site
        ]);
    }

    /**
     * Calculer la distance entre deux points géographiques
     */
    private function calculateDistance($lat1, $lon1, $lat2, $lon2): float
    {
        $earthRadius = 6371000; // Rayon de la Terre en mètres

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat/2) * sin($dLat/2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLon/2) * sin($dLon/2);

        $c = 2 * atan2(sqrt($a), sqrt(1-$a));

        return $earthRadius * $c;
    }
}
